from rest_framework import serializers

from Masters.mini_serializers import EmpMiniSerializer, EmployeeMiniSerializer
from .models import Department, Category, Employee, EmployeeAttendance, Leave, Holiday, SalaryRecord, Expense

class DepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = '__all__'
        read_only_fields = ['code', 'created_on', 'updated_on']

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'
        read_only_fields = ['code', 'created_on', 'updated_on']

class EmployeeSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = Employee
        fields = [
            'id', 'employee_no', 'name', 'photo', 'email', 'phone', 'salary',
            'department', 'department_name', 'category', 'category_name', 'joining_date',
            'is_active', 'created_on'
        ]
        read_only_fields = ['code', 'created_on', 'updated_on']

class EmployeeAttendanceSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.name', read_only=True)
    employee = EmpMiniSerializer(read_only=True)
    employee_id = serializers.UUIDField(write_only=True, required=False)

    class Meta:
        model = EmployeeAttendance
        fields = [
            'id', 'employee', 'employee_id', 'employee_name', 'date', 'is_present',
            'check_in_time', 'check_out_time', 'remarks',
            'created_on', 'modified_on'
        ]
        read_only_fields = ['code', 'employee','created_on', 'modified_on']

    def validate(self, data):
        employee_id = data.get('employee_id')
        if employee_id:
            try:
                employee = Employee.objects.get(id=employee_id)
                data['employee'] = employee
            except Employee.DoesNotExist:
                raise serializers.ValidationError(f"Employee with ID {employee_id} does not exist")
        
        date = data.get('date')
        employee = data.get('employee')
        if date and employee:
            existing_record = EmployeeAttendance.objects.filter(employee=employee,date=date).first()
            if existing_record:
                for key, value in data.items():
                    setattr(existing_record, key, value)
                existing_record.save()
                return existing_record
        
        return data

    def create(self, validated_data):
        # Remove employee_id from validated_data if it exists
        validated_data.pop('employee_id', None)
        return super().create(validated_data)

class BulkEmployeeAttendanceUploadSerializer(serializers.Serializer):
    file = serializers.FileField()
    date = serializers.DateField(required=False)

class LeaveSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.name', read_only=True)
    leave_type_display = serializers.CharField(source='get_leave_type_display', read_only=True)

    class Meta:
        model = Leave
        fields = [
            'id', 'employee', 'employee_name', 'leave_type', 'leave_type_display',
            'start_date', 'end_date', 'reason', 'is_approved',
            'created_on', 'modified_on'
        ]
        read_only_fields = ['code','created_at', 'modified_on']

class HolidaySerializer(serializers.ModelSerializer):
    class Meta:
        model = Holiday
        fields = '__all__'

class SalaryCalculationSerializer(serializers.Serializer):
    employee_id = serializers.IntegerField()
    month = serializers.IntegerField(min_value=1, max_value=12)
    year = serializers.IntegerField()

class DeductionsSerializer(serializers.Serializer):
    absent_deduction = serializers.DecimalField(max_digits=10, decimal_places=2)
    lwp_deduction = serializers.DecimalField(max_digits=10, decimal_places=2)
    sick_leave_deduction = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_deductions = serializers.DecimalField(max_digits=10, decimal_places=2)

class SalaryCalculationResponseSerializer(serializers.Serializer):
    employee_name = serializers.CharField()
    basic_salary = serializers.DecimalField(max_digits=10, decimal_places=2)
    month = serializers.IntegerField()
    year = serializers.IntegerField()
    total_working_days = serializers.IntegerField()
    present_days = serializers.IntegerField()
    absent_days = serializers.IntegerField()
    holiday_days = serializers.IntegerField()
    sick_leave_days = serializers.IntegerField()
    per_day_salary = serializers.DecimalField(max_digits=10, decimal_places=2)
    salary_components = serializers.DictField()
    deductions = serializers.DictField()
    net_salary = serializers.DecimalField(max_digits=10, decimal_places=2)

class BulkHolidayUploadSerializer(serializers.Serializer):
    file = serializers.FileField()
    year = serializers.IntegerField(required=False)
    file_type = serializers.ChoiceField(choices=['excel', 'pdf'], required=True)

    def validate_file(self, value):
        if value.name.endswith(('.xlsx', '.xls')):
            return value
        elif value.name.endswith('.pdf'):
            return value
        raise serializers.ValidationError("Only Excel (.xlsx, .xls) or PDF (.pdf) files are allowed")

from rest_framework.validators import UniqueTogetherValidator

class SalaryRecordSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.name', read_only=True)
    department_name = serializers.CharField(source='employee.department.name', read_only=True)
    category_name = serializers.CharField(source='employee.category.name', read_only=True)
    payment_date = serializers.DateField(required=False)
    employee_id = serializers.PrimaryKeyRelatedField(
        queryset=Employee.objects.all(),
        source='employee',
        write_only=True,
        allow_null=True
    )

    class Meta:
        model = SalaryRecord
        read_only_fields = ['code', 'created_on', 'modified_on', 'employee']
        fields = [
            'id', 'employee', 'employee_id', 'employee_name', 'department_name',
            'category_name', 'month', 'year', 'basic_salary', 'total_working_days',
            'present_days', 'absent_days', 'holiday_days', 'per_day_salary',
            'working_days_salary', 'holiday_salary', 'total_salary', 'net_salary',
            'is_paid', 'payment_date', 'transcaction_id', 'created_on', 'modified_on'
        ]
        validators = [
            UniqueTogetherValidator(
                queryset=SalaryRecord.objects.all(),
                fields=['employee_id', 'month', 'year'],
                message="Salary record already exists for this employee, month, and year."
            )
        ]

    def validate(self, data):
        if data.get('is_paid') and not data.get('payment_date'):
            raise serializers.ValidationError("Payment date is required when marking as paid")
        return data
    
class ExpenseSerializer(serializers.ModelSerializer):

    class Meta:
        model = Expense
        fields = [
            'id', 'name', 'quantity', 'price', 'transaction_id',
            'seller_phone', 'bill_image', 'date', 'created_on', 'modified_on'
        ]
        read_only_fields = ['code', 'created_on', 'modified_on']

class ExpenseSummarySerializer(serializers.Serializer):
    total_expenses = serializers.DecimalField(max_digits=10, decimal_places=2)
    recent_expenses = ExpenseSerializer(many=True) 
    
class SimpleExpenseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Expense
        fields = ['name', 'date', 'seller_phone', 'price', 'quantity', 'bill_image']