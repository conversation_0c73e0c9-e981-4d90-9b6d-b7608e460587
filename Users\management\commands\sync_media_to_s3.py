import os
import urllib.parse
import re
from django.core.management.base import BaseCommand
from django.conf import settings
from boto3.session import Session
from mimetypes import guess_type
from django.db import transaction
from django.db.models import Q
from Masters.models import Student

class Command(BaseCommand):
    help = 'Sync local media files to AWS S3 and update Student.photo and application_form URLs in DB'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without making changes',
        )
        parser.add_argument(
            '--skip-upload',
            action='store_true',
            help='Skip the file upload process',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        skip_upload = options['skip_upload']

        if dry_run:
            self.stdout.write(self.style.WARNING("🚧 Running in dry-run mode - no changes will be made"))

        # Step 1: Fix malformed URLs in DB
        self.fix_malformed_urls(dry_run)

        # Step 2: Upload files to S3 (unless skipped)
        if not skip_upload:
            self.upload_files_to_s3(dry_run)
        else:
            self.stdout.write(self.style.WARNING("⏭️ Skipping file upload as requested"))

        # Step 3: Verify no bad URLs remain
        self.verify_fixes()

        self.stdout.write(self.style.SUCCESS("✅ All operations completed successfully!"))

    @transaction.atomic
    def fix_malformed_urls(self, dry_run=False):
        """Fix all malformed URLs in the database for both photo and application_form fields"""
        self.stdout.write("🔧 Fixing malformed URLs...")

        students_with_bad_urls = Student.objects.filter(
            Q(photo__contains='127.0.0.1:8000') |
            Q(photo__contains='localhost:8000') |
            Q(photo__startswith='/media/') |
            Q(application_form__contains='127.0.0.1:8000') |
            Q(application_form__contains='localhost:8000') |
            Q(application_form__startswith='/media/')
        )

        updated_count = 0
        for student in students_with_bad_urls.iterator():
            updated = False

            # Fix photo field
            old_photo_url = student.photo
            new_photo_url = self.clean_url(old_photo_url)
            if old_photo_url and new_photo_url != old_photo_url:
                if not dry_run:
                    student.photo = new_photo_url
                    updated = True

            # Fix application_form field
            old_app_url = student.application_form
            new_app_url = self.clean_url(old_app_url)
            if old_app_url and new_app_url != old_app_url:
                if not dry_run:
                    student.application_form = new_app_url
                    updated = True

            if updated and not dry_run:
                student.save()
                updated_count += 1
                self.stdout.write(self.style.SUCCESS(f"✅ Fixed Student {student.id}:"))
                self.stdout.write(f"   photo: {old_photo_url} -> {new_photo_url}")
                self.stdout.write(f"   application_form: {old_app_url} -> {new_app_url}")

            elif dry_run and (new_photo_url != old_photo_url or new_app_url != old_app_url):
                updated_count += 1
                self.stdout.write(self.style.WARNING(f"🚧 Would fix Student {student.id}:"))
                self.stdout.write(f"   photo: {old_photo_url} -> {new_photo_url}")
                self.stdout.write(f"   application_form: {old_app_url} -> {new_app_url}")

        self.stdout.write(self.style.SUCCESS(f"🔧 {'Would fix' if dry_run else 'Fixed'} {updated_count} students in total"))

    def s3_url_for_path(self, relative_path):
        """Construct a full S3 URL for a given relative path"""
        relative_path = relative_path.lstrip('/')
        return f"https://{settings.AWS_STORAGE_BUCKET_NAME}.s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com/{relative_path}"

    def clean_url(self, url):
        """Clean and fix a malformed URL"""
        if not url:
            return url

        problematic_prefixes = [
            "http://127.0.0.1:8000/media/",
            "http://localhost:8000/media/",
            "/media/",
        ]

        for prefix in problematic_prefixes:
            if url.startswith(prefix):
                remainder = url[len(prefix):]
                decoded = urllib.parse.unquote(remainder)

                # Decode multiple times if necessary
                while '%3A' in decoded.lower():
                    decoded = urllib.parse.unquote(decoded)

                # Remove leading 'media/' to avoid duplication
                if decoded.startswith('media/'):
                    decoded = decoded[len('media/'):]

                # Return clean S3 URL
                return self.s3_url_for_path(f"media/{decoded}")

        # Handle double encoded S3 URL
        if 'https%3A' in url.lower():
            decoded = urllib.parse.unquote(url)
            s3_pattern = rf'https://{settings.AWS_STORAGE_BUCKET_NAME}\.s3\.{settings.AWS_S3_REGION_NAME}\.amazonaws\.com/.*'
            match = re.search(s3_pattern, decoded)
            if match:
                return match.group(0)

        return url

    def upload_files_to_s3(self, dry_run=False):
        """Upload local media files to AWS S3 and update DB URLs for photo and application_form"""
        try:
            media_root = settings.MEDIA_ROOT
            if not media_root or not os.path.exists(media_root):
                self.stdout.write(self.style.WARNING("MEDIA_ROOT does not exist, skipping file upload"))
                return

            self.stdout.write("📤 Uploading files to S3...")

            session = Session(
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_S3_REGION_NAME,
            )
            s3 = session.resource('s3')
            bucket = s3.Bucket(settings.AWS_STORAGE_BUCKET_NAME)

            for root, dirs, files in os.walk(media_root):
                for filename in files:
                    local_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(local_path, media_root).replace("\\", "/")
                    s3_path = f"media/{relative_path}"

                    # Check if file exists on S3
                    try:
                        s3.Object(settings.AWS_STORAGE_BUCKET_NAME, s3_path).load()
                        self.stdout.write(f"File already exists in S3: {s3_path}")
                    except:
                        content_type, _ = guess_type(local_path)
                        extra_args = {'ContentType': content_type} if content_type else {}

                        if not dry_run:
                            try:
                                bucket.upload_file(local_path, s3_path, ExtraArgs=extra_args)
                                self.stdout.write(self.style.SUCCESS(f"📤 Uploaded: {s3_path}"))
                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"❌ Failed to upload {s3_path}: {str(e)}"))
                                continue
                        else:
                            self.stdout.write(self.style.WARNING(f"📤 Would upload: {s3_path}"))

                    # Update DB URLs for photo and application_form fields that reference this file but aren't S3 URLs
                    s3_url = self.s3_url_for_path(s3_path)
                    try:
                        if not dry_run:
                            updated_photo = Student.objects.filter(
                                photo__icontains=relative_path
                            ).exclude(
                                photo__startswith='https://'
                            ).update(photo=s3_url)

                            updated_app_form = Student.objects.filter(
                                application_form__icontains=relative_path
                            ).exclude(
                                application_form__startswith='https://'
                            ).update(application_form=s3_url)

                            if updated_photo or updated_app_form:
                                self.stdout.write(self.style.SUCCESS(
                                    f"✅ Updated {updated_photo} photo(s) and {updated_app_form} application_form(s) with S3 URL: {s3_url}"
                                ))
                        else:
                            would_update_photo = Student.objects.filter(
                                photo__icontains=relative_path
                            ).exclude(
                                photo__startswith='https://'
                            ).count()

                            would_update_app_form = Student.objects.filter(
                                application_form__icontains=relative_path
                            ).exclude(
                                application_form__startswith='https://'
                            ).count()

                            if would_update_photo or would_update_app_form:
                                self.stdout.write(self.style.WARNING(
                                    f"✅ Would update {would_update_photo} photo(s) and {would_update_app_form} application_form(s) with S3 URL: {s3_url}"
                                ))
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"❌ Failed to update DB for {s3_url}: {str(e)}"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Critical error in upload_files_to_s3: {str(e)}"))
            raise

    def verify_fixes(self):
        """Check if any malformed URLs remain in the DB for photo or application_form"""
        self.stdout.write("🔍 Verifying fixes...")

        bad_urls_qs = Student.objects.filter(
            Q(photo__contains='127.0.0.1:8000') |
            Q(photo__contains='localhost:8000') |
            Q(photo__startswith='/media/') |
            Q(application_form__contains='127.0.0.1:8000') |
            Q(application_form__contains='localhost:8000') |
            Q(application_form__startswith='/media/')
        )

        bad_urls_count = bad_urls_qs.count()

        if bad_urls_count > 0:
            self.stdout.write(self.style.ERROR(f"❌ Found {bad_urls_count} students with malformed URLs remaining!"))
            for student in bad_urls_qs.iterator():
                self.stdout.write(f"   Student ID {student.id}: photo={student.photo}, application_form={student.application_form}")
        else:
            self.stdout.write(self.style.SUCCESS("✅ All URLs verified and clean!"))
