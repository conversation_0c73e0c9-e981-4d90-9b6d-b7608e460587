from functools import wraps
from django.core.cache import cache
from django.conf import settings
import hashlib
import json

def cache_response(timeout=None):
    """
    Decorator to cache API responses
    Usage: @cache_response(timeout=300)  # Cache for 5 minutes
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Generate cache key based on request path and query parameters
            cache_key = f"view_{request.path}_{hashlib.md5(request.GET.urlencode().encode()).hexdigest()}"
            
            # Try to get response from cache
            response = cache.get(cache_key)
            if response is not None:
                return response
            
            # If not in cache, get response from view
            response = view_func(request, *args, **kwargs)
            
            # Cache the response
            if timeout is None:
                timeout = settings.CACHE_TTL
            cache.set(cache_key, response, timeout=timeout)
            
            return response
        return _wrapped_view
    return decorator

def invalidate_cache_pattern(pattern):
    """
    Decorator to invalidate cache keys matching a pattern
    Usage: @invalidate_cache_pattern('view_/api/users/*')
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            response = view_func(request, *args, **kwargs)
            
            # Get all keys matching the pattern
            keys = cache.keys(pattern)
            if keys:
                cache.delete_many(keys)
            
            return response
        return _wrapped_view
    return decorator 