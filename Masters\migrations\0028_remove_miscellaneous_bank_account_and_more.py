# Generated by Django 5.2.3 on 2025-06-23 16:00

import Users.storage_backends
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Masters', '0027_miscellaneous_bank_name'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='miscellaneous',
            name='bank_account',
        ),
        migrations.AlterField(
            model_name='student',
            name='application_form',
            field=models.ImageField(blank=True, max_length=500, null=True, storage=Users.storage_backends.MediaStorage(), upload_to='application_photos/'),
        ),
        migrations.AlterField(
            model_name='student',
            name='photo',
            field=models.ImageField(blank=True, max_length=500, null=True, storage=Users.storage_backends.MediaStorage(), upload_to='student_photos/'),
        ),
    ]
