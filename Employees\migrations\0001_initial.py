# Generated by Django 5.1.3 on 2025-05-12 15:14

import django.core.validators
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('employee_no', models.CharField(blank=True, max_length=20, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='employee_photos/')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone', models.CharField(max_length=15)),
                ('address', models.TextField(blank=True, null=True)),
                ('joining_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeAttendance',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('date', models.DateField()),
                ('is_present', models.BooleanField(default=True)),
                ('check_in_time', models.TimeField(blank=True, null=True)),
                ('check_out_time', models.TimeField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['-date', 'employee__name'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('name', models.CharField(max_length=100)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('transaction_id', models.CharField(max_length=100, unique=True)),
                ('seller_phone', models.CharField(max_length=15)),
                ('bill_image', models.ImageField(blank=True, null=True, upload_to='expense_bills/')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'verbose_name': 'Expense',
                'verbose_name_plural': 'Expenses',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Holiday',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('name', models.CharField(max_length=100)),
                ('date', models.DateField(unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Leave',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('leave_type', models.CharField(choices=[('CL', 'Casual Leave'), ('SL', 'Sick Leave'), ('PL', 'Privilege Leave'), ('LWP', 'Leave Without Pay')], max_length=3)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('reason', models.TextField()),
                ('is_approved', models.BooleanField(default=False)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='SalaryRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('modified_on', models.DateTimeField(auto_now=True, null=True)),
                ('is_deleted', models.BooleanField(blank=True, default=False, null=True)),
                ('month', models.IntegerField()),
                ('year', models.IntegerField()),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_working_days', models.IntegerField()),
                ('present_days', models.IntegerField()),
                ('absent_days', models.IntegerField()),
                ('holiday_days', models.IntegerField()),
                ('per_day_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('working_days_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('holiday_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('net_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('code', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['-year', '-month'],
            },
        ),
    ]
