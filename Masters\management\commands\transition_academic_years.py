from django.core.management.base import BaseCommand
from django.utils import timezone
from Masters.models import Student, AcademicYear
from django.db import transaction

class Command(BaseCommand):
    help = 'Transition students to next academic year if their current year is ending'

    def handle(self, *args, **options):
        today = timezone.now().date()
        
        # Get all active students
        students = Student.objects.filter(
            is_join=True,
            is_deleted=False
        ).select_related('academic_year')
        
        transitioned = 0
        failed = 0
        
        for student in students:
            try:
                with transaction.atomic():
                    if student.transition_to_next_academic_year():
                        transitioned += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'Successfully transitioned student {student.name} to next academic year'
                            )
                        )
            except Exception as e:
                failed += 1
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to transition student {student.name}: {str(e)}'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Academic year transition completed. Successfully transitioned: {transitioned}, Failed: {failed}'
            )
        ) 