from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings

@shared_task
def send_email_task():
    """
    Sends an email using the configured SMTP settings.
    """
    subject = 'Celery Test Email'
    message = 'This is a test email sent from a Celery task.'
    email_from = settings.EMAIL_HOST_USER
    recipient_list = ['<EMAIL>']
    
    try:
        send_mail(subject, message, email_from, recipient_list)
        result = "Em<PERSON> sent successfully!"
        print(result)
    except Exception as e:
        result = f"Failed to send email: {e}"
        print(result)
        
    return result 