from datetime import timedelta
from matplotlib.dates import relativedelta
from rest_framework import serializers

from Employees.models import Category
from Masters.mini_serializers import AcademicMiniYearSerializer, BankNameMiniYearSerializer, ClassMiniSerializer, EducationalOfficerMiniSerializer, FeeTermMiniSerializer, FeesMiniSerializer, SectionMiniSerializer, StMiniSerializer, StudentMiniSerializer, StudentYearlyFeesMiniSerializer
from .models import (
    AcademicYear, Caste, Class, EducationalOfficer, InventoryItem, Miscellaneous, Section, StoreCategory, Student,
    FeeTerm, Fees, Attendance, SubCaste, SubCategory, StudentYearlyFees, BankName, StudentTestResult, StudentTestSubjectMark
)
from django.utils import timezone
from django.db.models import Sum
from django.db import models 



class AcademicYearSerializer(serializers.ModelSerializer):
    end_date = serializers.DateField(write_only=True, required=False)
    
    class Meta:
        model = AcademicYear
        fields = '__all__'
        read_only_fields = ['code', 'created_on', 'modified_on']

    def create(self, validated_data):
        end_date = validated_data.pop('end_date', None)
        if end_date:
            validated_data['last_date'] = end_date
            
        # Calculate name based on start_date and last_date
        start_year = validated_data['start_date'].year
        end_year = validated_data['last_date'].year if validated_data.get('last_date') else start_year
        validated_data['name'] = f"{start_year}-{end_year}"
        
        return super().create(validated_data)

    def update(self, instance, validated_data):
        end_date = validated_data.pop('end_date', None)
        if end_date:
            validated_data['last_date'] = end_date
            
        # Recalculate name if either start_date or last_date is being updated
        if 'start_date' in validated_data or 'last_date' in validated_data:
            start_year = validated_data.get('start_date', instance.start_date).year
            end_year = validated_data.get('last_date', instance.last_date).year if validated_data.get('last_date') or instance.last_date else start_year
            validated_data['name'] = f"{start_year}-{end_year}"
            
        return super().update(instance, validated_data)


class ClassSerializer(serializers.ModelSerializer):
    class Meta:
        model = Class
        fields = '__all__'
        read_only_fields = ['code', 'created_on', 'modified_on']


class SectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Section
        fields = '__all__'
        read_only_fields = ['code', 'created_on', 'modified_on']

    # def validate(self, data):
    #     if self.instance:
    #         qs = Section.objects.filter(
    #             name=data.get('name', self.instance.name),
    #             class_name=data.get('class_name', self.instance.class_name),
    #             group=data.get('group', self.instance.group),
    #             batch=data.get('batch', self.instance.batch)
    #         ).exclude(pk=self.instance.pk)
    #     else:
    #         qs = Section.objects.filter(
    #             name=data['name'],
    #             class_name=data['class_name'],
    #             group=data['group'],
    #             batch=data['batch']
    #         )
    #     if qs.exists():
    #         raise serializers.ValidationError("Section with this Name, Class, Group, and Batch already exists.")
    #     return data


# ---------- Student Serializers ----------


class CasteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Caste
        fields = ['id', 'code', 'name', 'created_on', 'modified_on']
        read_only_fields = ['id', 'code', 'created_on', 'modified_on']




class SubCasteSerializer(serializers.ModelSerializer):
    caste = CasteSerializer(many=False, read_only=True)
    caste_id = serializers.PrimaryKeyRelatedField(queryset=Caste.objects.all(),required=False,write_only=True)


    class Meta:
        model = SubCaste
        fields = ['id', 'code', 'name', 'caste','caste_id','created_on', 'modified_on']
        read_only_fields = ['id', 'code', 'caste','created_on', 'modified_on']

    def create(self, validated_data):
        caste = validated_data.pop('caste_id')
        return SubCaste.objects.create(caste=caste, **validated_data)

    def update(self, instance, validated_data):
        caste = validated_data.pop('caste_id', None)
        if caste:
            instance.caste = caste
        return super().update(instance, validated_data)
    



class BankNameSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankName
        fields = ['id', 'code', 'name', 'created_on', 'modified_on']
        read_only_fields = ['id', 'code', 'created_on', 'modified_on']


class FeeTermSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeeTerm
        fields = '__all__'
        read_only_fields = ['student']


class FeesSerializer(serializers.ModelSerializer):
    student_name = serializers.CharField(source='student.name', read_only=True)
    academic_year = serializers.PrimaryKeyRelatedField(queryset=AcademicYear.objects.all(),required=False,write_only=True)
    bank_name = BankNameMiniYearSerializer(many=False, read_only=True)
    bank_name_id = serializers.PrimaryKeyRelatedField(queryset=BankName.objects.all(),required=False,write_only=True)

    class Meta:
        model = Fees
        fields = ['id', 'student', 'student_name', 'amount', 'payment_date', 'turn', 'created_on', 'modified_on', 'receipt', 'academic_year','payment_mode', 'bank_account', 'transaction_number','receipt_no','bank_name','bank_name_id']
        read_only_fields = ['id', 'code', 'created_on', 'modified_on','bank_name', 'receipt_no']

    def validate(self, data):
        amount = data.get('amount')
        payment_date = data.get('payment_date', timezone.now().date())
        student = data.get('student')
        academic_year = data.get('academic_year')

        if amount is not None:
            if amount <= 0:
                raise serializers.ValidationError("Amount must be greater than 0")

        payment_mode = data.get('payment_mode')
        if payment_mode in ['upi', 'card', 'cheque']:
            if not data.get('transaction_number'):
                raise serializers.ValidationError({
                    'transaction_number': f'Transaction number is required for {payment_mode} payments'
                })

        # Proceed only if student is provided
        if student:
            if student.date_of_admission < student.academic_year.start_date:
                previous_yearly_fees = StudentYearlyFees.objects.filter(
                    student=student,
                    academic_year__start_date__lt=student.academic_year.start_date,
                    is_active=True
                ).order_by('-academic_year__start_date')

                expected_academic_year = (
                    previous_yearly_fees.first().academic_year if previous_yearly_fees.exists()
                    else student.academic_year
                )
            else:
                expected_academic_year = student.academic_year

            if academic_year and academic_year != expected_academic_year:
                raise serializers.ValidationError(
                    f"Invalid academic year. Expected {expected_academic_year.name} based on student's admission date and pending fees."
                )

            data['academic_year'] = expected_academic_year

        return data


    def create(self, validated_data):
        student = validated_data['student']
        amount = validated_data['amount']
        payment_date = validated_data.get('payment_date', timezone.now().date())
        turn = validated_data['turn']
        academic_year = validated_data['academic_year']
        payment_mode = validated_data.get('payment_mode', 'cash')
        bank_account = validated_data.get('bank_account')
        transaction_number = validated_data.get('transaction_number')
        bank_name = validated_data.get('bank_name_id')

        fee = Fees.objects.create(
            student=student,
            amount=amount,
            payment_date=payment_date,
            turn=turn,
            academic_year=academic_year,
            payment_mode=payment_mode,
            bank_account=bank_account,
            transaction_number=transaction_number,
            bank_name=bank_name,
        )

        student.calculate_pending_fees()

        return fee


from rest_framework.validators import UniqueValidator


class EducationalOfficerSerializer(serializers.ModelSerializer):

    class Meta:
        model = EducationalOfficer
        read_only_fields = ['id','code','phone_number']
        fields = ['id', 'name',]
        
        
class InitialStudentSerializer(serializers.ModelSerializer):
    admission_no = serializers.CharField(
        validators=[UniqueValidator(queryset=Student.objects.filter(is_deleted=False), message="Admission number already exists.")]
    )
    class_name = ClassMiniSerializer(read_only=True)
    class_name_id = serializers.PrimaryKeyRelatedField(queryset=Class.objects.all(), source='class_name', write_only=True, allow_null=True,required=False)
    # photo = serializers.ImageField(required=False)

    caste = CasteSerializer(read_only=True)
    caste_id = serializers.PrimaryKeyRelatedField(queryset=Caste.objects.all(), source='class_name', write_only=True, allow_null=True,required=False)

    sub_caste = SubCasteSerializer(read_only=True)
    sub_caste_id = serializers.PrimaryKeyRelatedField(queryset=SubCaste.objects.all(), source='class_name', write_only=True, allow_null=True,required=False)

    educational_officer = EducationalOfficerMiniSerializer(read_only=True)
    educational_officer_id = serializers.PrimaryKeyRelatedField(queryset=EducationalOfficer.objects.all(),source='educational_officer',write_only=True,required=False)

    committed_fees = serializers.FloatField()
    initial_fee_paid = serializers.FloatField()

    class Meta:
        model = Student
        read_only_fields = ['code','class_name', 'caste','sub_caste','educational_officer','created_on', 'modified_on']
        fields = ['id','name', 'father_name', 'class_name','class_name_id','photo','phone_numbers','committed_fees', 'initial_fee_paid', 'no_of_turns','status', 'group', 'batch', 'date_of_admission','admission_no','permanent_address','correcspondent_address','previous_school','caste','caste_id','sub_caste','sub_caste_id','educational_officer','educational_officer_id']
    
    def create(self, validated_data):
        date_of_admission = validated_data.get('date_of_admission', timezone.now().date())
        start_year = date_of_admission.year 
        end_year = start_year + 2

        academic_year = AcademicYear.objects.filter(start_date__year=start_year).first()

        if not academic_year:
            start_date = date_of_admission.replace(month=6, day=1)
            end_date = start_date + relativedelta(months=24) - timedelta(days=1)
            academic_year = AcademicYear.objects.create(
                name=f"{start_year}-{end_year}",
                start_date=start_date,
                last_date=end_date
            )

        validated_data['academic_year'] = academic_year
        
        # Calculate pending fees
        committed_fees = float(validated_data.get('committed_fees', 0))
        initial_fee_paid = float(validated_data.get('initial_fee_paid', 0))
        validated_data['pending_fees'] = committed_fees - initial_fee_paid
        
        student = super().create(validated_data)
        
        # Create yearly fees record
        StudentYearlyFees.objects.create(
            student=student,
            academic_year=academic_year,
            committed_fees=committed_fees,
            initial_fee_paid=initial_fee_paid,
            pending_fees=validated_data['pending_fees'],
            is_active=True
        )
        
        # Generate fee terms
        student.generate_fee_terms()
        
        return student

class StudentYearlyFeesSerializer(serializers.ModelSerializer):
    academic_year = AcademicYearSerializer(read_only=True)
    academic_year_id = serializers.PrimaryKeyRelatedField(
        queryset=AcademicYear.objects.all(),
        source='academic_year',
        write_only=True
    )

    class Meta:
        model = StudentYearlyFees
        fields = [
            'id', 'code', 'academic_year', 'academic_year_id',
            'committed_fees', 'initial_fee_paid', 'pending_fees',
            'is_active', 'created_on', 'modified_on'
        ]
        read_only_fields = ['code', 'pending_fees']


class StudentSerializer(serializers.ModelSerializer):

    academic_year = AcademicMiniYearSerializer(read_only=True)
    class_name = ClassMiniSerializer(read_only=True)
    section = SectionMiniSerializer(read_only=True)

    academic_year_id = serializers.PrimaryKeyRelatedField(queryset=AcademicYear.objects.all(), required=False)
    class_name_id = serializers.PrimaryKeyRelatedField(queryset=Class.objects.all(), source='class_name', write_only=True, allow_null=True)
    section_id = serializers.PrimaryKeyRelatedField(queryset=Section.objects.all(), source='section', write_only=True, allow_null=True)

    fee_terms = FeeTermMiniSerializer(source='feeterm_set', many=True, read_only=True)
    fees = FeesMiniSerializer(source='fees_set', many=True, read_only=True)
    yearly_fees = StudentYearlyFeesMiniSerializer(many=True, read_only=True)
    committed_fees = serializers.FloatField()
    initial_fee_paid = serializers.FloatField()
    educational_officer = EducationalOfficerMiniSerializer(read_only=True)
    educational_officer_id = serializers.PrimaryKeyRelatedField(queryset=EducationalOfficer.objects.all(),source='educational_officer',write_only=True)
    
    caste = CasteSerializer(read_only=True)
    caste_id = serializers.PrimaryKeyRelatedField(queryset=Caste.objects.all(), source='caste', write_only=True, allow_null=True)

    sub_caste = SubCasteSerializer(read_only=True)
    sub_caste_id = serializers.PrimaryKeyRelatedField(queryset=SubCaste.objects.all(), source='sub_caste', write_only=True, allow_null=True)

    photo_url = serializers.SerializerMethodField()

    # pending_fees = serializers.FloatField()
    # def get_committed_fees(self, obj):
        # return int(obj.committed_fees) if obj.committed_fees % 1 == 0 else float(obj.committed_fees)

    # def get_initial_fee_paid(self, obj):
        # return int(obj.initial_fee_paid) if obj.initial_fee_paid % 1 == 0 else float(obj.initial_fee_paid)
    # def get_pending_fees(self, obj):
        # return int(obj.pending_fees) if obj.pending_fees % 1 == 0 else float(obj.pending_fees)
    class Meta:
        model = Student
        fields = [
            'id', 'name', 'father_name', 'photo',
            'phone_numbers', 'academic_year', 'academic_year_id',
            'class_name', 'class_name_id', 'section', 'section_id',
            'group', 'batch', 'status', 'date_of_admission', 'admission_no','dob','student_aadhar','father_aadhar','mother_aadhar','application_form','photo_url',
            'no_of_turns', 'committed_fees', 'initial_fee_paid',
            'pending_fees', 'is_bookes_given', 'is_uniform_given', 'is_bag_given',
            'created_on', 'modified_on', 'fee_terms', 'fees', 'yearly_fees','is_join','educational_officer', 'educational_officer_id','permanent_address','correcspondent_address','previous_school','caste','caste_id','sub_caste','sub_caste_id'
        ]
        read_only_fields = ['code','pending_fees', 'photo_url','created_on', 'modified_on','educational_officer','caste','sub_caste']



    def get_photo_url(self, obj):
        request = self.context.get('request')
        if obj.photo:
            return obj.photo.url if request is None else request.build_absolute_uri(obj.photo.url)
        return None
    
    def validate(self, data):
        # Validate section belongs to class
        section = data.get('section')
        class_name = data.get('class_name')
        
        if section and class_name and section.class_name != class_name:
            raise serializers.ValidationError({
                'section_id': 'Selected section does not belong to the selected class'
            })
        
        return data

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make all fields optional during updates
        if self.instance is not None:
            for field in self.fields:
                if field not in self.Meta.read_only_fields:
                    self.fields[field].required = False

    def update(self, instance, validated_data):
        committed_fees = validated_data.get('committed_fees', instance.committed_fees)
        initial_fee_paid = validated_data.get('initial_fee_paid', instance.initial_fee_paid)
        validated_data['pending_fees'] = float(committed_fees) - float(initial_fee_paid)
        
        instance = super().update(instance, validated_data)
        instance.generate_fee_terms()
        
        return instance


# ---------- Attendance Serializer ----------

class AttendanceSerializer(serializers.ModelSerializer):
    # student_id = serializers.IntegerField(write_only=True, required=False)
    student = StMiniSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(queryset=Student.objects.all(), source='student', write_only=True, allow_null=True)
    student_name = serializers.CharField(source='student.name', read_only=True)
    date = serializers.DateField(required=False)
    is_present = serializers.BooleanField()
    
    def validate(self, data):
        if 'student_id' in data:
            try:
                Student.objects.get(id=data['student_id'])
            except Student.DoesNotExist:
                raise serializers.ValidationError(f"Student with ID {data['student_id']} does not exist")
        return data
    
    def create(self, validated_data):
        # Get or create attendance record
        student_id = validated_data.get('student_id')
        date = validated_data.get('date', timezone.now().date())
        
        try:
            attendance = Attendance.objects.get(student_id=student_id,date=date)
            attendance.is_present = validated_data['is_present']
            attendance.save()
            return attendance
        except Attendance.DoesNotExist:
            return super().create(validated_data)
    
    class Meta:
        model = Attendance
        fields = ['id', 'student','student_id', 'student_name', 'date', 'is_present']
        read_only_fields = ['id', 'code','student','student_name']


class TermPendingFeesSerializer(serializers.Serializer):
    term = serializers.IntegerField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    term_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    paid_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    pending_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    is_paid = serializers.BooleanField()


class BulkAttendanceUploadSerializer(serializers.Serializer):
    file = serializers.FileField()

    def validate_file(self, value):
        if not value.name.endswith('.xlsx'):
            raise serializers.ValidationError("Only Excel (.xlsx) files are allowed")
        return value



class AttendanceBulkCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Attendance
        fields = ['student', 'date', 'is_present']
        read_only_fields = ['code', 'created_on', 'modified_on']


# Comment out TestMarksSerializer if not already done
# class TestMarksSerializer(serializers.ModelSerializer):
#     student_name = serializers.CharField(source='student.name', read_only=True)
#     percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
#     # student_id = serializers.IntegerField(write_only=True)
#     student_id = serializers.PrimaryKeyRelatedField(queryset=Student.objects.all(), source='student', write_only=True, allow_null=True)

#     class Meta:
#         model = TestMarks
#         fields = [
#             'id', 'student_id', 'student_name', 'test_name', 
#             'test_date', 'marks_obtained', 'total_marks', 
#             'percentage','created_on', 'modified_on'
#         ]
#         read_only_fields = ['code','created_on', 'modified_on']

#     def validate(self, data):
#         if data['marks_obtained'] > data['total_marks']:
#             raise serializers.ValidationError("Marks obtained cannot be greater than total marks")
#         return data

#     # def create(self, validated_data):
#     #     student_id = validated_data.pop('student_id')
#     #     try:
#     #         student = Student.objects.get(id=student_id)
#     #         validated_data['student'] = student
#     #         return super().create(validated_data)
#     #     except Student.DoesNotExist:
#     #         raise serializers.ValidationError(f"Student with ID {student_id} does not exist")

#     def create(self, validated_data):
#         return super().create(validated_data)

class BulkTestMarksUploadSerializer(serializers.Serializer):
    file = serializers.FileField()
    test_name = serializers.CharField(max_length=100)
    test_date = serializers.DateField()
    total_marks = serializers.DecimalField(max_digits=5, decimal_places=2)
    # subject = serializers.CharField(max_length=100)

    def validate_file(self, value):
        if not value.name.endswith('.xlsx'):
            raise serializers.ValidationError("Only Excel (.xlsx) files are allowed")
        return value

    def validate_total_marks(self, value):
        if value <= 0:
            raise serializers.ValidationError("Total marks must be greater than 0")
        return value


class StudentSectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Student
        fields = ['id', 'name', 'admission_no', 'group', 'batch']

class SectionDetailSerializer(serializers.ModelSerializer):
    students = serializers.SerializerMethodField()

    class Meta:
        model = Section
        fields = ['id', 'name', 'class_name', 'group', 'batch', 'capacity', 'students']

    def get_students(self, obj):
        students = Student.objects.filter(
            section=obj,
            is_deleted=False
        )
        return StudentSectionSerializer(students, many=True).data


from rest_framework import serializers
from .models import StoreCategory, SubCategory, InventoryItem

class StoreCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = StoreCategory
        fields = ['id', 'name']

class SubCategorySerializer(serializers.ModelSerializer):
    store_category = StoreCategorySerializer(read_only=True)
    store_category_id = serializers.PrimaryKeyRelatedField(queryset=StoreCategory.objects.all(), source='store_category', write_only=True)
    
    class Meta:
        model = SubCategory
        fields = ['id', 'name', 'store_category', 'store_category_id']

class InventoryItemSerializer(serializers.ModelSerializer):
    store_category = StoreCategorySerializer(read_only=True)
    store_category_id = serializers.PrimaryKeyRelatedField(queryset=StoreCategory.objects.all(), source='store_category', write_only=True)
    subcategory = SubCategorySerializer(read_only=True)
    subcategory_id = serializers.PrimaryKeyRelatedField(
        queryset=SubCategory.objects.all(), 
        source='subcategory', 
        write_only=True
    )
    
    class Meta:
        model = InventoryItem
        fields = [
            'id', 'name', 'store_category', 'store_category_id', 
            'subcategory', 'subcategory_id', 'quantity', 
            'unit', 'threshold', 'status', 'location', 
            'supplier', 'price', 'image','is_deleted'
        ]
        
        

class MiscellaneousSerializer(serializers.ModelSerializer):
    student = StudentMiniSerializer(read_only=True)
    student_id = serializers.PrimaryKeyRelatedField(queryset=Student.objects.all(), source='student', write_only=True)
    mis_pending_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    bank_name = BankNameMiniYearSerializer(many=False, read_only=True)
    bank_name_id = serializers.PrimaryKeyRelatedField(queryset=BankName.objects.all(),required=False,write_only=True,source='bank_name')

    class Meta:
        model = Miscellaneous
        fields = [
            'id', 'code','student', 'student_id',
            'category', 'custom_category',
            'amount', 'paided_amount', 'mis_pending_amount',
            'payment_mode', 'transaction_number','receipt_no','payment_date','bank_name','bank_name_id'
            
        ]
        read_only_fields = ['mis_pending_amount', 'code']

    def validate(self, data):
        if not self.instance and 'amount' not in data:
            raise serializers.ValidationError({
                'amount': 'Amount is required for new records'
            })

        amount = data.get('amount')
        paided_amount = data.get('paided_amount', 0)

        if self.instance and amount is None:
            amount = self.instance.amount

        if paided_amount > amount:
            raise serializers.ValidationError({
                'paided_amount': 'Paid amount cannot be greater than total amount'
            })

        payment_mode = data.get('payment_mode')
        if payment_mode in ['upi', 'card', 'cheque']:
            if not data.get('transaction_number'):
                raise serializers.ValidationError({
                    'transaction_number': f'Transaction number is required for {payment_mode} payments'
                })
            # if not data.get('bank_account'):
            #     raise serializers.ValidationError({
            #         'bank_account': f'Bank account is required for {payment_mode} payments'
            #     })

        return data

    def create(self, validated_data):
        amount = validated_data.get('amount', 0)
        paided_amount = validated_data.get('paided_amount', 0)
        validated_data['mis_pending_amount'] = amount - paided_amount
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        if 'amount' not in validated_data:
            validated_data['amount'] = instance.amount

        amount = validated_data['amount']
        paided_amount = validated_data.get('paided_amount', instance.paided_amount)
        validated_data['mis_pending_amount'] = amount - paided_amount
        validated_data['modified_by'] = self.context['request'].user
        return super().update(instance, validated_data)


class BulkStudentMessageSerializer(serializers.Serializer):
    message = serializers.CharField()


class BulkStudentUploadSerializer(serializers.Serializer):
    file = serializers.FileField()

    def validate_file(self, value):
        if not (value.name.endswith('.xlsx') or value.name.endswith('.xls')):
            raise serializers.ValidationError("Only Excel (.xlsx, .xls) files are allowed")
        return value


class StudentTestSubjectMarkSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudentTestSubjectMark
        fields = ['id', 'subject', 'marks_obtained', 'total_marks', 'rank']

class StudentTestResultSerializer(serializers.ModelSerializer):
    subject_marks = StudentTestSubjectMarkSerializer(many=True)
    student_name = serializers.CharField(source='student.name', read_only=True)

    class Meta:
        model = StudentTestResult
        fields = ['id', 'student', 'student_name', 'test_name', 'test_date', 'total_marks', 'rank', 'subject_marks']

    def create(self, validated_data):
        subject_marks_data = validated_data.pop('subject_marks')
        test_result = StudentTestResult.objects.create(**validated_data)
        for subject_data in subject_marks_data:
            StudentTestSubjectMark.objects.create(test_result=test_result, **subject_data)
        return test_result

    def update(self, instance, validated_data):
        subject_marks_data = validated_data.pop('subject_marks', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if subject_marks_data is not None:
            instance.subject_marks.all().delete()
            for subject_data in subject_marks_data:
                StudentTestSubjectMark.objects.create(test_result=instance, **subject_data)
        return instance