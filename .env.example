# MongoDB Configuration
MONGODB_NAME=spoorthi_crm
MONGODB_URI=mongodb://localhost:27017/spoorthi_crm

# For MongoDB Atlas (cloud) - Use this format for production
# MONGODB_URI=mongodb+srv://username:<EMAIL>/spoorthi_crm?retryWrites=true&w=majority

# For Render deployment with MongoDB Atlas
# MONGODB_NAME=spoorthi_crm
# MONGODB_URI=***********************************************************************************************************

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_STORAGE_BUCKET_NAME=your_bucket_name
AWS_S3_REGION_NAME=your_region

# Other environment variables
USE_S3=False
DEBUG=True

# Django Secret Key
SECRET_KEY=your-secret-key-here
