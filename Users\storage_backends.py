from storages.backends.s3boto3 import S3Boto3Storage
import logging

logger = logging.getLogger("s3_upload")

class MediaStorage(S3Boto3Storage):
    location = 'media'
    file_overwrite = False
    default_acl = None

    def _save(self, name, content):
        try:
            logger.info(f"Uploading {name} to S3 bucket at {self.location}")
            return super()._save(name, content)
        except Exception as e:
            logger.error(f"Failed to upload {name} to S3: {e}", exc_info=True)
            raise