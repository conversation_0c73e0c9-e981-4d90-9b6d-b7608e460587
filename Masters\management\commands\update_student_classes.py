from django.core.management.base import BaseCommand
from django.db import transaction
from Masters.models import Student, Class
from decimal import Decimal

class Command(BaseCommand):
    help = 'Update student records with specific class assignment, fees, and group/batch choices'

    def add_arguments(self, parser):
        parser.add_argument(
            '--class-uuid',
            type=str,
            default='5f4a1059-6a6c-4240-b060-c297d0d11ad3',
            help='UUID of the class to assign to all students'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes'
        )

    def handle(self, *args, **options):
        class_uuid = options['class_uuid']
        dry_run = options['dry_run']
        
        # Get the target class
        try:
            target_class = Class.objects.get(id=class_uuid)
            self.stdout.write(
                self.style.SUCCESS(f'Found target class: {target_class.name}')
            )
        except Class.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Class with UUID {class_uuid} does not exist')
            )
            return
        
        # Get all active students
        students = Student.objects.filter(
            is_join=True,
            is_deleted=False
        )
        
        self.stdout.write(f'Found {students.count()} active students to update')
        
        # Define groups that should have 40000 committed fees
        groups_40000 = ['SPARK III', 'SPARK GIRLS', 'SPARK BOYS', 'SMPL']
        
        updated_count = 0
        failed_count = 0
        
        for student in students:
            try:
                with transaction.atomic():
                    # Store original values for logging
                    original_class = student.class_name
                    original_committed_fees = student.committed_fees
                    original_initial_fee_paid = student.initial_fee_paid
                    
                    # Calculate new values
                    new_initial_fee_paid = Decimal('0.00')
                    if student.group in groups_40000:
                        new_committed_fees = Decimal('40000.00')
                    else:
                        new_committed_fees = Decimal('50000.00')
                    new_pending_fees = new_committed_fees - new_initial_fee_paid
                    
                    if not dry_run:
                        # Use update() to bypass the complex save() method logic
                        Student.objects.filter(id=student.id).update(
                            class_name=target_class,
                            initial_fee_paid=new_initial_fee_paid,
                            committed_fees=new_committed_fees,
                            pending_fees=new_pending_fees
                        )
                        updated_count += 1
                        
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'Updated student {student.name} (ID: {student.id}): '
                                f'Class: {original_class} → {target_class.name}, '
                                f'Committed Fees: {original_committed_fees} → {new_committed_fees}, '
                                f'Initial Fees Paid: {original_initial_fee_paid} → {new_initial_fee_paid}, '
                                f'Group: {student.group}'
                            )
                        )
                    else:
                        updated_count += 1
                        self.stdout.write(
                            f'[DRY RUN] Would update student {student.name} (ID: {student.id}): '
                            f'Class: {original_class} → {target_class.name}, '
                            f'Committed Fees: {original_committed_fees} → {new_committed_fees}, '
                            f'Initial Fees Paid: {original_initial_fee_paid} → {new_initial_fee_paid}, '
                            f'Group: {student.group}'
                        )
                        
            except Exception as e:
                failed_count += 1
                self.stdout.write(
                    self.style.ERROR(
                        f'Failed to update student {student.name} (ID: {student.id}): {str(e)}'
                    )
                )
        
        # Summary
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'[DRY RUN] Would update {updated_count} students, {failed_count} failed'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated {updated_count} students, {failed_count} failed'
                )
            )
        
        # Show group distribution
        self.stdout.write('\nGroup distribution:')
        group_counts = {}
        for student in students:
            group = student.group
            group_counts[group] = group_counts.get(group, 0) + 1
        
        for group, count in sorted(group_counts.items()):
            fee_amount = '40000' if group in groups_40000 else '50000'
            self.stdout.write(f'  {group}: {count} students (Committed Fees: {fee_amount})') 