from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.conf import settings
import logging
import os
import boto3
from datetime import datetime

class Command(BaseCommand):
    help = 'Test S3 connection and file operations'
    
    def add_arguments(self, parser):
        parser.add_argument('--verbose', action='store_true', help='Verbose output')
    
    def handle(self, *args, **options):
        self.stdout.write("=== S3 CONNECTION TEST ===")
        
        # Test 1: Check Django settings
        self.stdout.write("1. Checking Django S3 settings...")
        try:
            storage_backend = settings.DEFAULT_FILE_STORAGE
            self.stdout.write(f"   Storage backend: {storage_backend}")
            
            if hasattr(settings, 'AWS_STORAGE_BUCKET_NAME'):
                self.stdout.write(f"   Bucket: {settings.AWS_STORAGE_BUCKET_NAME}")
            if hasattr(settings, 'AWS_S3_REGION_NAME'):
                self.stdout.write(f"   Region: {settings.AWS_S3_REGION_NAME}")
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"   Settings error: {e}"))
            return
        
        # Test 2: Test Django storage
        self.stdout.write("\n2. Testing Django default storage...")
        try:
            # Create test file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            test_filename = f"test_files/django_test_{timestamp}.txt"
            test_content = ContentFile(f"Django S3 test - {timestamp}")
            
            # Save file
            saved_name = default_storage.save(test_filename, test_content)
            self.stdout.write(f"   ✅ File saved: {saved_name}")
            
            # Get URL
            file_url = default_storage.url(saved_name)
            self.stdout.write(f"   ✅ File URL: {file_url}")
            
            # Check if exists
            exists = default_storage.exists(saved_name)
            self.stdout.write(f"   ✅ File exists: {exists}")
            
            # Get file size
            try:
                size = default_storage.size(saved_name)
                self.stdout.write(f"   ✅ File size: {size} bytes")
            except Exception as e:
                self.stdout.write(f"   ⚠️  Could not get file size: {e}")
            
            # Clean up
            if options['verbose']:
                self.stdout.write("   Cleaning up test file...")
            default_storage.delete(saved_name)
            self.stdout.write("   ✅ Test file deleted")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"   ❌ Django storage test failed: {e}"))
            if options['verbose']:
                import traceback
                self.stdout.write(traceback.format_exc())
        
        # Test 3: Direct boto3 test
        self.stdout.write("\n3. Testing direct boto3 connection...")
        try:
            if hasattr(settings, 'AWS_ACCESS_KEY_ID') and hasattr(settings, 'AWS_SECRET_ACCESS_KEY'):
                s3 = boto3.client(
                    's3',
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                    region_name=getattr(settings, 'AWS_S3_REGION_NAME', 'us-east-1')
                )
                
                # List buckets
                response = s3.list_buckets()
                self.stdout.write(f"   ✅ Connected to AWS S3")
                self.stdout.write(f"   Available buckets: {len(response['Buckets'])}")
                
                if hasattr(settings, 'AWS_STORAGE_BUCKET_NAME'):
                    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
                    try:
                        s3.head_bucket(Bucket=bucket_name)
                        self.stdout.write(f"   ✅ Target bucket accessible: {bucket_name}")
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"   ❌ Cannot access bucket {bucket_name}: {e}"))
                        
            else:
                self.stdout.write("   ⚠️  AWS credentials not found in settings")
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"   ❌ Boto3 test failed: {e}"))
        
        self.stdout.write("\n=== END S3 TEST ===")