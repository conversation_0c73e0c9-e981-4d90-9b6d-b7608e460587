
from django.core.management.base import BaseCommand
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.files.storage import default_storage
import tempfile
import os

class Command(BaseCommand):
    help = 'Test file upload to S3 with a real file'
    
    def handle(self, *args, **options):
        self.stdout.write("Testing S3 file upload with real file...")
        
        # Create a temporary image file
        try:
            from PIL import Image
            import io
            
            # Create a simple test image
            img = Image.new('RGB', (100, 100), color='red')
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # Create uploaded file object
            uploaded_file = SimpleUploadedFile(
                name='test_upload.png',
                content=img_buffer.read(),
                content_type='image/png'
            )
            
            # Save to storage
            saved_path = default_storage.save('test_uploads/test_image.png', uploaded_file)
            file_url = default_storage.url(saved_path)
            
            self.stdout.write(self.style.SUCCESS(f"✅ File uploaded successfully!"))
            self.stdout.write(f"   Path: {saved_path}")
            self.stdout.write(f"   URL: {file_url}")
            
            # Verify file exists
            if default_storage.exists(saved_path):
                self.stdout.write("✅ File exists in storage")
                size = default_storage.size(saved_path)
                self.stdout.write(f"   Size: {size} bytes")
            else:
                self.stdout.write(self.style.ERROR("❌ File not found in storage"))
            
        except ImportError:
            self.stdout.write(self.style.ERROR("PIL not installed. Install with: pip install Pillow"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Upload test failed: {e}"))