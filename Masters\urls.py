from django.urls import path, include
from .views import AcademicYearListCreateView, AcademicYearDetailView, AverageFeeCollectionAPIView, CasteDetailView, CasteListCreateView, ClassAgainstSectionView,ClassListCreateView, ClassDetailView, EODetail, EoList, InventoryItemDetailView, InventoryItemListCreateView, MiscellaneousDetailView, MiscellaneousListCreateView, SectionByClassBatchView,SectionListCreateView, SectionDetailView, StoreCategoryDetailView, StoreCategoryListCreateView, StudentListCreateView, StudentDetailView,FeeTermListCreateView, FeeTermDetailView,FeesListCreateView, FeesDetailView,AttendanceListCreateView, AttendanceDetailView,StudentTermPendingFeesView, BulkAttendanceUploadView,StudentAttendanceView, SubCasteDetailView, SubCasteListCreateView, SubCategoryDetailView, SubCategoryListCreateView, TestMarksListCreateView,TestMarksDetailView, StudentTestMarksView,BulkTestMarksUploadView,FeeCollectionDetailsView,AbsentStudentsView,BulkStudentMessageView,BulkAbsentStudentMessageView, StudentYearlyFeesListCreateView, StudentYearlyFeesDetailView, BankNameListCreateView, BankNameDetailView, BulkTermPendingMessageView, BulkStudentUploadView, IndividualAttendanceMessageView, IndividualMarksMessageView, BulkMarksMessageView


urlpatterns = [

    path('caste/', CasteListCreateView.as_view(), name='class-list'),
    path('caste/<uuid:pk>/', CasteDetailView.as_view(), name='class-detail'),
    
    path('subcaste/', SubCasteListCreateView.as_view(), name='class-list'),
    path('subcaste/<uuid:pk>/', SubCasteDetailView.as_view(), name='class-detail'),
    
    path('bank/', BankNameListCreateView.as_view(), name='class-list'),
    path('bank/<uuid:pk>/', BankNameDetailView.as_view(), name='class-detail'),
    
    
    # AcademicYear
    path('academic-years/', AcademicYearListCreateView.as_view(), name='academic-year-list'),
    path('academic-years/<uuid:pk>/', AcademicYearDetailView.as_view(), name='academic-year-detail'),

    # Class
    path('classes/', ClassListCreateView.as_view(), name='class-list'),
    path('classes/<uuid:pk>/', ClassDetailView.as_view(), name='class-detail'),
    path('students/<uuid:section_id>/section/', SectionByClassBatchView.as_view(), name='students-by-section'),

    # Section
    path('sections/', SectionListCreateView.as_view(), name='section-list'),
    path('sections/<uuid:pk>/', SectionDetailView.as_view(), name='section-detail'),
    path('classes/<uuid:class_id>/sections/', ClassAgainstSectionView.as_view(), name='class-sections'),

    # Student
    path('students/', StudentListCreateView.as_view(), name='student-list'),
    path('students/<uuid:pk>/', StudentDetailView.as_view(), name='student-detail'),
    path('students/<uuid:pk>/term-pending-fees/', StudentTermPendingFeesView.as_view(), name='student-term-pending-fees'),
    path('students/bulk-upload/', BulkStudentUploadView.as_view(), name='bulk-student-upload'),

    # Fee Term
    path('fee-terms/', FeeTermListCreateView.as_view(), name='fee-term-list'),
    path('fee-terms/<uuid:pk>/', FeeTermDetailView.as_view(), name='fee-term-detail'),

    # Fees
    path('fees/', FeesListCreateView.as_view(), name='fees-list'),
    path('fees/<uuid:pk>/', FeesDetailView.as_view(), name='fees-detail'),

    # Attendance
    path('attendance/', AttendanceListCreateView.as_view(), name='attendance-list'),
    path('attendance/<uuid:pk>/', AttendanceDetailView.as_view(), name='attendance-detail'),
    path('attendance/bulk-upload/', BulkAttendanceUploadView.as_view(), name='bulk-attendance-upload'),
    path('attendance/student/<uuid:student_id>/', StudentAttendanceView.as_view(), name='student-attendance'),

    # Test Marks
    path('test-marks/', TestMarksListCreateView.as_view(), name='test-marks-list'),
    path('test-marks/<uuid:pk>/', TestMarksDetailView.as_view(), name='test-marks-detail'),
    path('test-marks/student/<uuid:student_id>/', StudentTestMarksView.as_view(), name='student-test-marks'),
    path('test-marks/bulk-upload/', BulkTestMarksUploadView.as_view(), name='bulk-test-marks-upload'),

    # Reports
    path('fees-collection/', FeeCollectionDetailsView.as_view(), name='fee-collection-details'),

    # Absent Students
    path('absent-students/<str:date>/', AbsentStudentsView.as_view(), name='absent-students'),

    # Messages
    path('messages/bulk-student/', BulkStudentMessageView.as_view(), name='bulk-student-message'),
    path('messages/bulk-absent-student/', BulkAbsentStudentMessageView.as_view(), name='bulk-absent-student-message'),
    path('messages/bulk-term-pending-message/', BulkTermPendingMessageView.as_view(), name='bulk-term-pending-message'),
    path('messages/individualattendance/', IndividualAttendanceMessageView.as_view(), name='individual-attendance-message'),
    path('messages/individual-marks/', IndividualMarksMessageView.as_view(), name='individual-marks-message'),
    path('messages/bulk-marks/', BulkMarksMessageView.as_view(), name='bulk-marks-message'),
    
    # Fee Collection Summary
    path('average-fee-collection/', AverageFeeCollectionAPIView.as_view(), name='average-fee-collection'),
    
    # Student Yearly Fees URLs
    path('students/<uuid:student_id>/yearly-fees/', StudentYearlyFeesListCreateView.as_view(), name='student-yearly-fees-list-create'),
    path('students/<uuid:student_id>/yearly-fees/<uuid:pk>/', StudentYearlyFeesDetailView.as_view(), name='student-yearly-fees-detail'),
    
    
    
    path('store-categories/', StoreCategoryListCreateView.as_view(), name='store-category-list'),
    path('store-categories/<uuid:pk>/', StoreCategoryDetailView.as_view(), name='store-category-detail'),
    
    # Sub Categories
    path('subcategories/', SubCategoryListCreateView.as_view(), name='subcategory-list'),
    path('subcategories/<uuid:pk>/', SubCategoryDetailView.as_view(), name='subcategory-detail'),
    
    # Inventory Items
    path('inventory/', InventoryItemListCreateView.as_view(), name='inventory-list'),
    path('inventory/<uuid:pk>/', InventoryItemDetailView.as_view(), name='inventory-detail'),
    
    path('miscellaneous/', MiscellaneousListCreateView.as_view(), name='miscellaneous-list-create'),
    path('miscellaneous/<uuid:pk>/mis/', MiscellaneousDetailView.as_view(), name='miscellaneous-detail'),
    
    path('eduofficer/',EoList.as_view(), name='eo-list-create'),
    path('eduofficer/<uuid:pk>/', EODetail.as_view(), name='eo-detail'),

    # path('student-attendance-summary/', StudentAttendanceSummaryView.as_view(), name='student-attendance-summary'),

]
