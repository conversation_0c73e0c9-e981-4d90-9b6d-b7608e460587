from rest_framework import serializers
from .models import (
    AcademicYear, Class, EducationalOfficer, Miscellaneous, Section, Student,
    FeeTerm, Fees, Attendance, StudentTestResult, StudentTestSubjectMark, StudentYearlyFees
)
from Employees.models import (
    Department, Category, Employee, EmployeeAttendance,
    Leave, Holiday, SalaryRecord
)

class AcademicMiniYearSerializer(serializers.ModelSerializer):
    class Meta:
        model = AcademicYear
        fields = ['id', 'name', 'start_date', 'last_date']

class ClassMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Class
        fields = ['id', 'name']

class BankNameMiniYearSerializer(serializers.ModelSerializer):
    class Meta:
        model = AcademicYear
        fields = ['id', 'name']


class SectionMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Section
        fields = ['id', 'name', 'class_name', 'group', 'batch']

class FeeTermMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeeTerm
        fields = ['id', 'term', 'amount', 'is_paid', 'start_date', 'end_date']

class FeesMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Fees
        fields = ['id', 'amount', 'payment_date', 'turn']

class AttendanceMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Attendance
        fields = ['id', 'date', 'is_present']

class StudentYearlyFeesMiniSerializer(serializers.ModelSerializer):
    academic_year = AcademicMiniYearSerializer(read_only=True)

    class Meta:
        model = StudentYearlyFees
        fields = [
            'id', 'academic_year', 'committed_fees', 
            'initial_fee_paid', 'pending_fees', 'is_active'
        ]


class StMiniSerializer(serializers.ModelSerializer):

    class Meta:
        model = Student
        fields = [
            'id', 'name',]
        
class StudentMiniSerializer(serializers.ModelSerializer):
    academic_year = AcademicMiniYearSerializer(read_only=True)
    class_name = ClassMiniSerializer(read_only=True)
    section = SectionMiniSerializer(read_only=True)

    class Meta:
        model = Student
        fields = [
            'id', 'name', 'admission_no', 'academic_year',
            'class_name', 'section', 'group', 'batch',
            'committed_fees', 'initial_fee_paid', 'pending_fees'
        ]

class DepartmentMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['id', 'name', 'description']

class CategoryMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'description']


class EmpMiniSerializer(serializers.ModelSerializer):

    class Meta:
        model = Employee
        fields = [
            'id', 'employee_no', 'name',]
        
class EmployeeMiniSerializer(serializers.ModelSerializer):
    department = DepartmentMiniSerializer(read_only=True)
    category = CategoryMiniSerializer(read_only=True)

    class Meta:
        model = Employee
        fields = [
            'id', 'employee_no', 'name', 'email', 'phone',
            'department', 'category', 'joining_date', 'is_active',
            'salary'
        ]

class EmployeeAttendanceMiniSerializer(serializers.ModelSerializer):
    employee = EmployeeMiniSerializer(read_only=True)

    class Meta:
        model = EmployeeAttendance
        fields = [
            'id', 'employee', 'date', 'is_present',
            'check_in_time', 'check_out_time'
        ]

class LeaveMiniSerializer(serializers.ModelSerializer):
    employee = EmployeeMiniSerializer(read_only=True)

    class Meta:
        model = Leave
        fields = [
            'id', 'employee', 'leave_type', 'start_date',
            'end_date', 'is_approved'
        ]

class HolidayMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Holiday
        fields = ['id', 'name', 'date', 'description']

class SalaryRecordMiniSerializer(serializers.ModelSerializer):
    employee = EmployeeMiniSerializer(read_only=True)

    class Meta:
        model = SalaryRecord
        fields = [
            'id', 'employee', 'month', 'year', 'basic_salary',
            'total_working_days', 'present_days', 'absent_days',
            'holiday_days', 'net_salary', 'is_paid', 'payment_date'
        ] 
        
        
class EducationalOfficerMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = EducationalOfficer
        fields = ['id', 'name']
        
        
class MiscellaneousMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Miscellaneous
        fields = ['id', 'name', 'description']

class StudentTestResultMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudentTestResult
        fields = ['id', 'test_name', 'test_date', 'total_marks', 'rank']

class StudentTestSubjectMarkMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudentTestSubjectMark
        fields = ['id', 'subject', 'marks_obtained', 'total_marks', 'rank']