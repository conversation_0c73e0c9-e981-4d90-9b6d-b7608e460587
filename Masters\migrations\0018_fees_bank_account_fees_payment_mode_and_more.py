# Generated by Django 5.2.1 on 2025-06-01 04:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Masters', '0017_student_application_form'),
    ]

    operations = [
        migrations.AddField(
            model_name='fees',
            name='bank_account',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='fees',
            name='payment_mode',
            field=models.CharField(choices=[('cash', 'Cash'), ('upi', 'UPI'), ('card', 'Card'), ('cheque', 'Cheque')], default='cash', max_length=10),
        ),
        migrations.AddField(
            model_name='fees',
            name='transaction_number',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='miscellaneous',
            name='bank_account',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='miscellaneous',
            name='payment_mode',
            field=models.CharField(choices=[('cash', 'Cash'), ('upi', 'UPI'), ('card', 'Card'), ('cheque', 'Cheque')], default='cash', max_length=10),
        ),
        migrations.AddField(
            model_name='miscellaneous',
            name='transaction_number',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
