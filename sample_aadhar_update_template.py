import pandas as pd

# Create sample data
sample_data = {
    'admission_no': [
        '2504034',
        '2504035', 
        '2504036',
        '2504037',
        '2504038'
    ],
    'student_aadhar': [
        '**************',
        '**************',
        '**************', 
        '**************',
        '**************'
    ]
}

# Create DataFrame
df = pd.DataFrame(sample_data)

# Save to Excel
df.to_excel('sample_aadhar_update.xlsx', index=False, sheet_name='Sheet1')

print("Sample Excel template created: sample_aadhar_update.xlsx")
print("\nTemplate format:")
print(df.to_string())
print("\nInstructions:")
print("1. Replace the sample data with your actual admission numbers and Aadhar numbers")
print("2. Make sure Aadhar numbers are exactly 12 digits (spaces are allowed)")
print("3. Save the file and run the command:")
print("   python manage.py update_student_aadhar --excel-file your_file.xlsx")
print("\nTo preview changes without updating:")
print("   python manage.py update_student_aadhar --excel-file your_file.xlsx --dry-run") 