from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin
from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid
from rest_framework_simplejwt.tokens import RefreshToken



class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **kwargs):
        if not email:
            raise TypeError('Users must have an email address')
        email = self.normalize_email(email)
        user = self.model(email=email, **kwargs)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password=None, **kwargs):
        if not password:
            raise TypeError('Password should not be none')
        user = self.create_user(email, password, **kwargs)
        user.is_superuser = True
        user.is_staff = True
        user.save()
        return user

class User(AbstractBaseUser, PermissionsMixin):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(max_length=255, db_index=True, blank=True, null=True,unique=True)
    is_email_verified = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now, blank=True)
    modified_on = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name='users_created', on_delete=models.RESTRICT, null=True)
    modified_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name='users_updated', on_delete=models.RESTRICT, null=True)
    is_staff = models.BooleanField(default=False)
    is_organizer = models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)
    is_employee = models.BooleanField(default=False)
    is_director = models.BooleanField(default=False)
    expires_at = models.DateTimeField(default=timezone.now)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self):
        return self.email or str(self.id)

    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def tokens(self):
        refresh = RefreshToken.for_user(self)
        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token)
        }


class BaseModel(models.Model):
    id = models.UUIDField( primary_key=True,default=uuid.uuid4, editable=False)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name='%(class)s_createdby', on_delete=models.RESTRICT, null=True)
    created_on = models.DateTimeField(auto_now_add=True, blank=True)
    modified_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name='%(class)s_modifiedby', on_delete=models.RESTRICT, null=True)
    modified_on = models.DateTimeField(blank=True, null=True, auto_now=True)
    is_deleted=models.BooleanField(blank=True, default=False, null=True)

    
    class Meta:
        abstract = True
    
class CoreModel(BaseModel):
    code = models.CharField(max_length=30, unique=True)
    
    def save(self, *args, **kwargs):
        print(args, kwargs, self.code, self.__class__)
        if (self.code == "" or self.code == None):
            self.code = getcode(self.__class__)
        super().save(*args, **kwargs)

    class Meta:
        abstract = True


def getcode(model, ):
    model_name = model.__name__
    prefix = model.CODE_PREFIX
    try:
        sequence_code, sc = SequenceCode.objects.get_or_create(name= model_name)
        sequence = sequence_code.sequence + 1
        SequenceCode.objects.filter(name= model_name).update(sequence= sequence)
    except:
        sequence= 1

    nxtIdlen = len(str(sequence))
    if nxtIdlen == 1:
        prenum = '0000'
    elif nxtIdlen == 2:
        prenum = '000'
    elif nxtIdlen == 3:
        prenum = '00'
    elif nxtIdlen == 4:
        prenum = '0'
    else:
        prenum =''
        
    return str(prefix) + str(prenum) + str(sequence)


class SequenceCode(models.Model):
    name = models.CharField(max_length=100, null=True, blank=True)
    sequence = models.IntegerField(default=0,null=True, blank=True)
    prefix = models.CharField(max_length=100, null=True, blank=True)

    __str__ = lambda self: str(self.name)
