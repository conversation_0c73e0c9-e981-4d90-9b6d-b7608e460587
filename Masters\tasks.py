from django.core.cache import cache
from django.conf import settings
from .models import Student, Fe<PERSON>, Attendance, FeeTerm, StudentYearlyFees
from django.db.models import Sum
from django.utils import timezone

def calculate_student_pending_fees(student_id):
    """Calculate pending fees for a student"""
    try:
        student = Student.objects.get(id=student_id)
        student.calculate_pending_fees()
        # Invalidate cache for this student
        cache.delete(f'student_{student_id}_fees')
        return True
    except Student.DoesNotExist:
        return False

def update_attendance_cache(student_id, date):
    """Update attendance cache for a student"""
    cache_key = f'attendance_{student_id}_{date}'
    try:
        attendance = Attendance.objects.get(student_id=student_id, date=date)
        cache.set(cache_key, {
            'is_present': attendance.is_present,
            'date': attendance.date,
            'student_id': attendance.student_id
        }, timeout=settings.CACHE_TTL)
    except Attendance.DoesNotExist:
        cache.delete(cache_key)

def update_fee_term_status(fee_term_id):
    """Update fee term status and cache"""
    try:
        fee_term = FeeTerm.objects.get(id=fee_term_id)
        fee_term.is_paid = float(fee_term.paid_amount) >= float(fee_term.amount)
        fee_term.save()
        
        # Invalidate related caches
        cache.delete(f'student_{fee_term.student_id}_fees')
        cache.delete(f'fee_term_{fee_term_id}')
        return True
    except FeeTerm.DoesNotExist:
        return False

def update_student_yearly_fees(student_id, academic_year_id):
    """Update student yearly fees and cache"""
    try:
        yearly_fees = StudentYearlyFees.objects.get(
            student_id=student_id,
            academic_year_id=academic_year_id
        )
        
        # Calculate total fees paid
        total_paid = float(yearly_fees.initial_fee_paid)
        fee_payments = Fees.objects.filter(
            student_id=student_id,
            payment_date__gte=yearly_fees.academic_year.start_date,
            payment_date__lte=yearly_fees.academic_year.last_date
        )
        total_paid += float(fee_payments.aggregate(total=Sum('amount'))['total'] or 0)
        
        # Update pending fees
        yearly_fees.pending_fees = float(yearly_fees.committed_fees) - total_paid
        yearly_fees.save()
        
        # Invalidate related caches
        cache.delete(f'student_{student_id}_yearly_fees')
        cache.delete(f'student_{student_id}_fees')
        return True
    except StudentYearlyFees.DoesNotExist:
        return False 