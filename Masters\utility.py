from django.apps import apps
from django.db import transaction
from django.core.exceptions import ValidationError
import logging

logger = logging.getLogger(__name__)

def generate_receipt_no(start_year):
    """
    Generate a unique receipt number for the given academic year.
    Uses database-level atomic operations to prevent race conditions.
    """
    Fees = apps.get_model('Masters', 'Fees')
    Miscellaneous = apps.get_model('Masters', 'Miscellaneous')

    fee_prefix = f"{start_year}"
    misc_prefix = f"{start_year}"

    # Use atomic transaction to prevent race conditions
    with transaction.atomic():
        # Get the current maximum count from both models
        fee_receipts = Fees.objects.filter(
            receipt_no__startswith=fee_prefix,
            receipt_no__regex=f'^{fee_prefix}[0-9]+$'  # Ensure it's a valid format
        ).count()
        
        misc_receipts = Miscellaneous.objects.filter(
            receipt_no__startswith=misc_prefix,
            receipt_no__regex=f'^{misc_prefix}[0-9]+$'  # Ensure it's a valid format
        ).count()

        max_count = max(fee_receipts, misc_receipts) + 1
        receipt_no = f"{fee_prefix}{str(max_count).zfill(5)}"
        
        # Double-check for uniqueness (in case of edge cases)
        while (Fees.objects.filter(receipt_no=receipt_no).exists() or 
               Miscellaneous.objects.filter(receipt_no=receipt_no).exists()):
            max_count += 1
            receipt_no = f"{fee_prefix}{str(max_count).zfill(5)}"
            
        return receipt_no


# Alternative approach using database sequences (recommended)
def generate_receipt_no_with_sequence(start_year):
    """
    Alternative approach using the latest receipt number from database.
    More reliable for concurrent requests.
    """
    from django.db.models import Max
    from django.db import connection
    
    Fees = apps.get_model('Masters', 'Fees')
    Miscellaneous = apps.get_model('Masters', 'Miscellaneous')

    fee_prefix = f"{start_year}"
    
    with transaction.atomic():
        # Get the latest receipt number from both models
        fee_max = Fees.objects.filter(
            receipt_no__startswith=fee_prefix
        ).aggregate(
            max_no=Max('receipt_no')
        )['max_no']
        
        misc_max = Miscellaneous.objects.filter(
            receipt_no__startswith=fee_prefix
        ).aggregate(
            max_no=Max('receipt_no')
        )['max_no']
        
        # Extract the numeric part and find the maximum
        fee_num = 0
        misc_num = 0
        
        if fee_max:
            try:
                fee_num = int(fee_max.replace(fee_prefix, ''))
            except ValueError:
                fee_num = 0
                
        if misc_max:
            try:
                misc_num = int(misc_max.replace(fee_prefix, ''))
            except ValueError:
                misc_num = 0
        
        next_num = max(fee_num, misc_num) + 1
        return f"{fee_prefix}{str(next_num).zfill(5)}"