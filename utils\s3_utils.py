import boto3
import os
from django.conf import settings
import uuid
from dotenv import load_dotenv

load_dotenv()

def upload_file_to_s3(local_file_path, bucket_name, s3_key, aws_access_key_id=None, aws_secret_access_key=None, region_name=None):
    """
    Uploads a file to S3 and returns the S3 URL.
    Args:
        local_file_path (str): Path to the local file.
        bucket_name (str): Name of the S3 bucket.
        s3_key (str): S3 object key (path in bucket).
        aws_access_key_id (str, optional): AWS access key.
        aws_secret_access_key (str, optional): AWS secret key.
        region_name (str, optional): AWS region.
    Returns:
        str: S3 URL of the uploaded file.
    Raises:
        Exception: If upload fails.
    """
    session = boto3.session.Session(
        aws_access_key_id=aws_access_key_id or getattr(settings, 'AWS_ACCESS_KEY_ID', None),
        aws_secret_access_key=aws_secret_access_key or getattr(settings, 'AWS_SECRET_ACCESS_KEY', None),
        region_name=region_name or getattr(settings, 'AWS_S3_REGION_NAME', None),
    )
    s3 = session.resource('s3')
    bucket = s3.Bucket(bucket_name)
    try:
        bucket.upload_file(local_file_path, s3_key)
        s3_url = f"https://{bucket_name}.s3.{session.region_name}.amazonaws.com/{s3_key}"
        return s3_url
    except Exception as e:
        raise Exception(f"Failed to upload to S3: {str(e)}")


if __name__ == "__main__":
    # Create a random .txt file
    random_filename = f"test_upload_{uuid.uuid4().hex[:8]}.txt"
    local_path = os.path.join(os.getcwd(), random_filename)
    with open(local_path, "w") as f:
        f.write("This is a test file for S3 upload.\n")

    # S3 details from environment only
    bucket = os.getenv("AWS_STORAGE_BUCKET_NAME")
    print(f"Using bucket: {bucket}")
    region = os.getenv("AWS_S3_REGION_NAME")
    print(f"Using region: {region}")
    aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
    print(f"Using access key ID: {aws_access_key_id}")
    aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
    print(f"Using secret access key: {aws_secret_access_key}")
    s3_key = f"manual_uploads/{random_filename}"
    print(f"Using S3 key: {s3_key}")
    
    if not (bucket and region and aws_access_key_id and aws_secret_access_key):
        print("Missing S3 configuration in environment variables.")
    else:
        try:
            url = upload_file_to_s3(local_path, bucket, s3_key, aws_access_key_id, aws_secret_access_key, region)
            print(f"Uploaded to: {url}")
            # Write result to a .txt file
            result_file = f"s3_upload_result_{uuid.uuid4().hex[:8]}.txt"
            with open(result_file, "w") as rf:
                rf.write(f"S3 URL: {url}\n")
            print(f"Result written to {result_file}")
        except Exception as e:
            print(f"Error: {e}")
    # Clean up test file
    if os.path.exists(local_path):
        os.remove(local_path) 