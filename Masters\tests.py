import datetime
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from Masters.models import Student, AcademicYear, Class, Section
from django.utils import timezone

# Create your tests here.

class BulkTermPendingMessageSingleStudentTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        # Create required foreign keys
        academic_year, _ = AcademicYear.objects.get_or_create(name="2024-2025", defaults={"start_date": datetime.date(2024, 6, 1), "last_date": datetime.date(2025, 5, 31)})
        class_obj, _ = Class.objects.get_or_create(name="TestClass")
        section_obj, _ = Section.objects.get_or_create(name="A", class_name=class_obj, group="spark", batch="mpc")
        # Create the target student
        self.target_student = Student.objects.create(
            name="pramod",
            admission_no="PRM123",
            father_name="Test Father",
            phone_numbers=["9553717019"],
            academic_year=academic_year,
            class_name=class_obj,
            section=section_obj,
            group="SPARK BOYS",
            batch="MPC",
            gender="M",
            status="admission",
            date_of_admission=timezone.now().date(),
            no_of_turns=3,
            committed_fees=10000,
            initial_fee_paid=2000,
            pending_fees=8000,
        )
        # Create other students with different phone numbers
        for i in range(5):
            Student.objects.create(
                name=f"other_student_{i}",
                admission_no=f"OTH{i}",
                father_name="Other Father",
                phone_numbers=[f"900000000{i}"],
                academic_year=academic_year,
                class_name=class_obj,
                section=section_obj,
                group="SPARK BOYS",
                batch="MPC",
                gender="M",
                status="admission",
                date_of_admission=timezone.now().date(),
                no_of_turns=3,
                committed_fees=10000,
                initial_fee_paid=2000,
                pending_fees=8000,
            )

    def test_bulk_term_pending_message_only_targets_pramod(self):
        # Patch the SMS client to capture sent numbers
        from unittest.mock import patch
        with patch('utils.msg91.SpoorthiBulkSMSClient.send_bulk_sms') as mock_send_sms:
            mock_send_sms.return_value = {'status': 'success', 'message': 'SMS sent successfully', 'data': {}}
            url = reverse('bulk-term-pending-message')
            response = self.client.post(url, {}, format='json')
            self.assertEqual(response.status_code, 200)
            # Check that send_bulk_sms was called at least once
            self.assertTrue(mock_send_sms.called)
            # Collect all phone numbers that were sent SMS
            sent_numbers = []
            for call in mock_send_sms.call_args_list:
                sent_numbers.extend(call[0][0])  # call_args[0] is the 'numbers' argument
            # Only the target student's number should be present
            self.assertIn('9553717019', sent_numbers)
            # None of the other students' numbers should be present
            for i in range(5):
                self.assertNotIn(f'900000000{i}', sent_numbers)
