from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from rest_framework import serializers
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.core.exceptions import ValidationError

from Masters.utility import generate_receipt_no, generate_receipt_no_with_sequence
from Users.models import CoreModel, getcode

import logging

from Users.storage_backends import MediaStorage

logger = logging.getLogger(__name__)
# ---------- Academic Setup ----------

class Caste(CoreModel):
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name

    CODE_PREFIX = 'CAS'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Caste,'CAS')
        super(Caste, self).save(*args, **kwargs)    
        

class SubCaste(CoreModel):
    caste = models.ForeignKey(Caste, on_delete=models.CASCADE, related_name='subcastes')
    name = models.CharField(max_length=100)

    def __str__(self):
        return f"{self.name} ({self.caste.name})"
    
    CODE_PREFIX = 'SUBC'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(SubCaste,'SUBC')
        super(SubCaste, self).save(*args, **kwargs)
        

class BankName(CoreModel):
    name = models.CharField(max_length=50, unique=True)

    def __str__(self):
        return self.name

    CODE_PREFIX = 'BANK'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(BankName,'BANK')
        super(BankName, self).save(*args, **kwargs)
        
    
class AcademicYear(CoreModel):
    name = models.CharField(max_length=50, unique=True, blank=True)
    start_date = models.DateField()
    last_date = models.DateField(null=True, blank=True)

    def __str__(self):
        return self.name

    @property
    def months_duration(self):
        """Calculate the number of months between start_date and last_date"""
        if not self.last_date:
            return 12  # Default to 12 months if last_date is not set
        delta = relativedelta(self.last_date, self.start_date)
        return (delta.years * 12) + delta.months + 1  # Add 1 to include both start and end months

    def calculate_academic_period(self):
        start_year = self.start_date.year
        end_year = self.last_date.year if self.last_date else start_year
        
        return f"{start_year}-{end_year}"

    def save(self, *args, **kwargs):
        if not self.name:
            self.name = self.calculate_academic_period()
            
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(AcademicYear, 'AY')
            
        super().save(*args, **kwargs)

    CODE_PREFIX = 'AY'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(AcademicYear,'AY')
        super(AcademicYear, self).save(*args, **kwargs)
        
class Class(CoreModel):
    name = models.CharField(max_length=50, unique=True)

    def __str__(self):
        return self.name

    CODE_PREFIX = 'CLS'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Class,'CLS')
        super(Class, self).save(*args, **kwargs)

class Section(CoreModel):
    GROUP_CHOICES = [
        ('spark', 'Spark'),
        ('ipl', 'IPL'),
    ]

    BATCH_CHOICES = [
        ('mpc', 'MPC'),
        ('bipc', 'BIPC'),
    ]
    name = models.CharField(max_length=10)
    class_name = models.ForeignKey(Class, on_delete=models.CASCADE, related_name='sections')
    group = models.CharField(max_length=10, choices=GROUP_CHOICES)
    batch = models.CharField(max_length=10, choices=BATCH_CHOICES)
    # capacity = models.PositiveIntegerField(default=30)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.class_name.name} - {self.name} ({self.group} - {self.batch})"

    class Meta:
        # unique_together = ['name', 'class_name', 'batch']
        ordering = ['class_name', 'name']

    CODE_PREFIX = 'SEC'
        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Section,'SEC')
        
        # if Section.objects.filter(
        #     name=self.name,
        #     batch=self.batch,
        #     is_deleted=False
        # ).exclude(pk=self.pk).exists():
        #     raise ValidationError(f"Section {self.name} already exists for batch {self.get_batch_display()}")
            
        super(Section, self).save(*args, **kwargs)        
# ---------- Student ----------


class EducationalOfficer(CoreModel):
    name = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15, blank=True, null=True)


    def __str__(self):
        return self.name

    CODE_PREFIX = 'EO'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(EducationalOfficer,'EO')
        super(EducationalOfficer, self).save(*args, **kwargs)
    
    
    
    
class Student(CoreModel):
    STATUS_CHOICES = [
        ('reservation', 'Reservation'),
        ('admission', 'Admission'),
    ]

    GROUP_CHOICES = [
        ('CO-SPARK STAR', 'CO-SPARK STAR'),
        ('CO-SPARK  GIRLS', 'CO-SPARK  GIRLS'),
        ('CO-SPARK BOYS', 'CO-SPARK BOYS'),
        ('S-SPARK STAR', 'S-SPARK STAR'),
        ('S-SPARK BOYS', 'S-SPARK BOYS'),
        ('SPARK GIRLS', 'SPARK GIRLS'),
        ('SPARK BOYS', 'SPARK BOYS'),
        ('SPARK III', 'SPARK III'),
        ('SMPL', 'SMPL'),
    ]

    BATCH_CHOICES = [
        ('MPC', 'MPC'),
        ('BiPC', 'BIPC'),
        ('cec', 'CEC'),
        ('mec', 'MEC'),
    ]

    NO_OF_TURNS_CHOICES = [
        (1, '1'),
        (2, '2'),
        (3, '3'),
        (4, '4'),
        (5, '5'),
        (6, '6'),
        (7, '7'),
        (8, '8'),
    ]

    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    # Basic Information
    admission_no = models.CharField(max_length=20, unique=True, blank=True)
    name = models.CharField(max_length=100)
    father_name = models.CharField(max_length=100)
    photo = models.ImageField(upload_to='student_photos/', null=True, blank=True,max_length=500,storage=MediaStorage())

    # Contact Information
    phone_numbers = models.JSONField(default=list)

    # Academic Info
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.SET_NULL, null=True)
    class_name = models.ForeignKey(Class, on_delete=models.SET_NULL, null=True, blank=True)
    section = models.ForeignKey(Section, on_delete=models.SET_NULL, null=True, blank=True)
    group = models.CharField(max_length=20, choices=GROUP_CHOICES)
    batch = models.CharField(max_length=20, choices=BATCH_CHOICES)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, default='M')
    educational_officer = models.ForeignKey(EducationalOfficer, on_delete=models.SET_NULL, null=True, blank=True)

    # Admission
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    date_of_admission = models.DateField(default=timezone.now)
    no_of_turns = models.PositiveSmallIntegerField(choices=NO_OF_TURNS_CHOICES, default=3)
    
    dob = models.DateField(null=True, blank=True)
    student_aadhar = models.CharField(max_length=18, blank=True, null=True)
    father_aadhar = models.CharField(max_length=18, blank=True, null=True)
    mother_aadhar = models.CharField(max_length=18, blank=True, null=True)
    application_form = models.ImageField(upload_to='application_photos/', null=True, blank=True,max_length=500,storage=MediaStorage())
    # Fees
    committed_fees = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    initial_fee_paid = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    pending_fees = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    permanent_address = models.TextField(null=True, blank=True)
    correcspondent_address = models.TextField(null=True, blank=True)
    previous_school = models.CharField(max_length=100, null=True, blank=True)
    
    caste = models.ForeignKey(Caste, on_delete=models.SET_NULL, null=True, blank=True)
    sub_caste = models.ForeignKey(SubCaste, on_delete=models.SET_NULL, null=True, blank=True)

    is_bookes_given = models.BooleanField(default=False)
    is_uniform_given = models.BooleanField(default=False)
    is_bag_given = models.BooleanField(default=False)
    is_join = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    CODE_PREFIX = 'STD'

    def __str__(self):
        return f"{self.admission_no} - {self.name} - {self.class_name} {self.section}"

    def clean(self):
        if self.section and self.class_name and self.section.class_name != self.class_name:
            raise ValidationError({
                'section': 'Section must belong to the selected class'
            })

    def generate_fee_terms(self):
        """Generate fee terms for the student's current academic year."""
        if not self.academic_year:
            return

        # Get or create yearly fees record
        yearly_fees, created = StudentYearlyFees.objects.get_or_create(
            student=self,
            academic_year=self.academic_year,
            defaults={
                'committed_fees': self.committed_fees,
                'initial_fee_paid': self.initial_fee_paid,
                'is_active': True
            }
        )

        # If yearly fees already exists but committed fees don't match, update it
        if not created and yearly_fees.committed_fees != self.committed_fees:
            yearly_fees.committed_fees = self.committed_fees
            yearly_fees.save()
            # Delete existing terms to force regeneration
            FeeTerm.objects.filter(
                student=self, 
                academic_year=self.academic_year
            ).delete()

        # Calculate term amount based on current committed fees
        term_amount = float(self.committed_fees) / self.no_of_turns

        # Get existing terms for the current academic year
        existing_terms = FeeTerm.objects.filter(
            student=self, 
            academic_year=self.academic_year
        ).order_by('term')
        
        # If no terms exist or number of terms doesn't match, regenerate them
        if not existing_terms.exists() or existing_terms.count() != self.no_of_turns:
            # Delete existing terms
            existing_terms.delete()

            # Generate new terms
            months_in_year = getattr(self.academic_year, 'months_duration', 12)
            months_per_term = months_in_year // self.no_of_turns
            start_date = self.academic_year.start_date
            
            for i in range(self.no_of_turns):
                term_start = start_date + relativedelta(months=i * months_per_term)
                if i == self.no_of_turns - 1:
                    term_end = self.academic_year.last_date
                else:
                    term_end = (term_start + relativedelta(months=months_per_term - 1, days=32)).replace(day=1) - timedelta(days=1)

                FeeTerm.objects.create(
                    student=self,
                    academic_year=self.academic_year,
                    term=i + 1,
                    start_date=term_start,
                    end_date=term_end,
                    amount=term_amount
                )


    def calculate_pending_fees(self):
        """Calculate pending fees without modifying initial_fee_paid"""
        # Get the current academic year's fees
        current_year_fees = self.yearly_fees.filter(
            academic_year=self.academic_year,
            is_active=True
        ).first()
        
        if not current_year_fees:
            return
            
        # Use current initial_fee_paid value without modification
        total_paid = float(self.initial_fee_paid) if self.initial_fee_paid else 0
        
        # Get all fee payments for current academic year
        fee_payments = self.fees_set.filter(
            payment_date__gte=self.academic_year.start_date,
            payment_date__lte=self.academic_year.last_date
        )
        
        for payment in fee_payments:
            total_paid += float(payment.amount)
            
        # Calculate pending fees
        pending_fees = float(self.committed_fees) - total_paid
        
        # Update both student and yearly fees records
        self.pending_fees = pending_fees
        current_year_fees.pending_fees = pending_fees
        current_year_fees.save(update_fields=['pending_fees'])
        
        return pending_fees

    # Add this method to your Student model to replace calculate_pending_fees
    def transition_to_next_academic_year(self):
        """Transition student to the next academic year if current year is ending."""
        if not self.academic_year or not self.academic_year.last_date:
            return False
            
        today = timezone.now().date()
        if today < self.academic_year.last_date:
            return False
            
        # Find next academic year
        next_year = AcademicYear.objects.filter(
            start_date__gt=self.academic_year.start_date
        ).order_by('start_date').first()
        
        if not next_year:
            return False
            
        # Create new yearly fees record with reset values
        StudentYearlyFees.objects.create(
            student=self,
            academic_year=next_year,
            committed_fees=self.committed_fees,
            initial_fee_paid=0,  # Only reset here when transitioning years
            pending_fees=self.committed_fees,
            is_active=True
        )
        
        # Update student's academic year
        self.academic_year = next_year
        # DON'T reset initial_fee_paid here unless it's a year transition
        # self.initial_fee_paid = 0  # REMOVE THIS LINE
        self.pending_fees = self.committed_fees
        self.save()
        
        # Generate new fee terms for the next academic year
        self.generate_fee_terms()
        
        return True

    # Updated save method for Student model
    def save(self, *args, **kwargs):
        # Initialize original values if they don't exist (for new instances)
        if not hasattr(self, '_original_committed_fees'):
            self._original_committed_fees = self.committed_fees if self.pk else None
        if not hasattr(self, '_original_academic_year_id'):
            self._original_academic_year_id = self.academic_year_id if self.pk else None
        if not hasattr(self, '_original_no_of_turns'):
            self._original_no_of_turns = self.no_of_turns if self.pk else None
        if not hasattr(self, '_original_status'):
            self._original_status = self.status if self.pk else None
        if not hasattr(self, '_original_academic_year_months'):
            self._original_academic_year_months = self.academic_year.months_duration if self.pk and self.academic_year else None

        # Store original initial_fee_paid to prevent unwanted resets
        preserve_initial_fee = kwargs.pop('preserve_initial_fee', False)
        original_initial_fee = None
        
        if self.pk and not preserve_initial_fee:
            try:
                original = Student.objects.get(pk=self.pk)
                original_initial_fee = original.initial_fee_paid
            except Student.DoesNotExist:
                pass
        
        if self.id is None and (not getattr(self, 'code', None)):
            self.code = getcode(Student, self.CODE_PREFIX)

        if self.section and self.class_name and self.section.class_name != self.class_name:
            self.section = None

        should_regenerate = (
            not self.pk or
            (self.committed_fees != self._original_committed_fees) or
            (self.academic_year_id != self._original_academic_year_id) or
            (self.no_of_turns != self._original_no_of_turns) or
            (self.academic_year and self._original_academic_year_months and 
            self.academic_year.months_duration != self._original_academic_year_months)
        )

        if should_regenerate:
            self.generate_fee_terms()

        # Only call calculate_pending_fees if we're not preserving the initial fee
        if not preserve_initial_fee:
            self.calculate_pending_fees()

        if self.status == 'admission':
            self.is_join = True
        elif self.status == 'reservation':
            if not self._original_status or self._original_status == 'reservation':
                self.is_join = False
            elif self._original_status == 'admission':
                self.is_join = True

        # Restore original initial_fee_paid if it was changed unintentionally
        if original_initial_fee is not None and not preserve_initial_fee:
            # Only restore if the change wasn't intentional (detect if it was changed in this save)
            if hasattr(self, '_preserve_initial_fee_paid') and self._preserve_initial_fee_paid:
                self.initial_fee_paid = original_initial_fee

        super().save(*args, **kwargs)
        
        # Update the original values after successful save
        self._original_committed_fees = self.committed_fees
        self._original_academic_year_id = self.academic_year_id
        self._original_no_of_turns = self.no_of_turns
        self._original_status = self.status
        self._original_academic_year_months = self.academic_year.months_duration if self.academic_year else None
# ---------- Fee Term Tracking ----------

class FeeTerm(CoreModel):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE)
    term = models.PositiveIntegerField()
    start_date = models.DateField()
    end_date = models.DateField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    is_paid = models.BooleanField(default=False)
    paid_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    def __str__(self):
        return f"{self.student.name} - Term {self.term} ({self.start_date} to {self.end_date})"

    CODE_PREFIX = 'FT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(FeeTerm,'FT')
        super(FeeTerm, self).save(*args, **kwargs)

from django.db import IntegrityError


class Fees(CoreModel):
    PAYMENT_MODE_CHOICES = [
        ('cash', 'Cash'),
        ('upi', 'UPI'),
        ('card', 'Card'),
        ('cheque', 'Cheque'),
    ]
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    payment_date = models.DateField(default=timezone.now)
    turn = models.PositiveIntegerField(default=1)
    receipt = models.FileField(upload_to='fee_receipts/', null=True, blank=True)  # NEW
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, null=True, blank=True)
    receipt_no = models.CharField(max_length=20, unique=True, null=True, blank=True)

    payment_mode = models.CharField(max_length=10, choices=PAYMENT_MODE_CHOICES, default='cash')
    bank_name = models.ForeignKey(BankName, on_delete=models.CASCADE, null=True, blank=True)
    bank_account = models.CharField(max_length=100, null=True, blank=True)
    transaction_number = models.CharField(max_length=100, null=True, blank=True)
    
    
    def __str__(self):
        return f"{self.student.name} - {self.amount} - {self.payment_date}"

    class Meta:
        verbose_name = 'Fee Payment'
        verbose_name_plural = 'Fee Payments'

    CODE_PREFIX = 'FEE'

    def save(self, *args, **kwargs):
        # Generate code if it's a new instance
        if self.id is None and (self.code == "" or self.code is None):
            self.code = getcode(Fees, 'FEE')
            
        # Generate receipt number if it's a new instance and doesn't have one
        if not self.receipt_no:
            try:
                if self.academic_year:
                    start_year = self.academic_year.start_date.year
                elif self.student and self.student.academic_year:
                    start_year = self.student.academic_year.start_date.year
                else:
                    start_year = timezone.now().year
                    
                self.receipt_no = generate_receipt_no_with_sequence(start_year)
                
            except Exception as e:
                logger.error(f"Error generating receipt number: {str(e)}")
                # Fallback to timestamp-based receipt number
                import time
                self.receipt_no = f"{start_year}{int(time.time())}"
                
        try:
            super(Fees, self).save(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error saving fee record: {str(e)}")
            raise


# ---------- Attendance ----------

class Attendance(CoreModel):
    CODE_PREFIX = 'ATT'
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    is_present = models.BooleanField(default=True)

    class Meta:
        unique_together = ('student', 'date')

    CODE_PREFIX = 'ATT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Attendance,'ATT')
        super(Attendance, self).save(*args, **kwargs)
        
# --- New Test Result Models ---
class StudentTestResult(models.Model):
    student = models.ForeignKey('Student', on_delete=models.CASCADE)
    test_name = models.CharField(max_length=100)
    test_date = models.DateField(default=timezone.now)
    total_marks = models.DecimalField(max_digits=6, decimal_places=2)
    rank = models.PositiveIntegerField(null=True, blank=True)  # Overall rank

    class Meta:
        unique_together = ('student', 'test_name', 'test_date')

    def __str__(self):
        return f"{self.student.name} - {self.test_name} ({self.test_date})"

class StudentTestSubjectMark(models.Model):
    test_result = models.ForeignKey(StudentTestResult, related_name='subject_marks', on_delete=models.CASCADE)
    subject = models.CharField(max_length=100)
    marks_obtained = models.DecimalField(max_digits=5, decimal_places=2)
    total_marks = models.DecimalField(max_digits=5, decimal_places=2)
    rank = models.PositiveIntegerField(null=True, blank=True)  # Subject-wise rank

    class Meta:
        unique_together = ('test_result', 'subject')

    def __str__(self):
        return f"{self.test_result} - {self.subject}"

# --- Deprecate old TestMarks model (do not delete, just comment out) ---
# class TestMarks(CoreModel):
#     student = models.ForeignKey(Student, on_delete=models.CASCADE)
#     test_name = models.CharField(max_length=100)
#     subject = models.CharField(max_length=100)
#     marks_obtained = models.DecimalField(max_digits=5, decimal_places=2)
#     total_marks = models.DecimalField(max_digits=5, decimal_places=2)
#     test_date = models.DateField(default=timezone.now)
#
#     def __str__(self):
#         return f"{self.student.name} - {self.test_name} - {self.subject}"
#
#     class Meta:
#         unique_together = ('student', 'test_name', 'subject')
#
#     CODE_PREFIX = 'TM'
#
#     def save(self, *args, **kwargs):
#         if self.id is None and (self.code == "" or self.code == None):
#             self.code = getcode(TestMarks,'TM')
#         super(TestMarks, self).save(*args, **kwargs)

class StudentYearlyFees(CoreModel):
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='yearly_fees')
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE)
    committed_fees = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    initial_fee_paid = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    pending_fees = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['student', 'academic_year']
        ordering = ['academic_year__start_date']

    def __str__(self):
        return f"{self.student.name} - {self.academic_year.name} - {self.committed_fees}"

    CODE_PREFIX = 'SYF'

    def calculate_pending_fees(self):
        """Calculate pending fees for the current academic year"""
        # Use student's initial fee paid
        total_paid = float(self.student.initial_fee_paid) if self.student.initial_fee_paid else 0
        
        # Get all fee payments for this academic year
        fee_payments = self.student.fees_set.filter(
            payment_date__gte=self.academic_year.start_date,
            payment_date__lte=self.academic_year.last_date
        )
        
        # Add all fee payments to total_paid
        for payment in fee_payments:
            total_paid += float(payment.amount)
        
        # Calculate pending fees
        pending_fees = float(self.committed_fees) - total_paid
        
        # Update both records
        self.pending_fees = pending_fees
        self.student.pending_fees = pending_fees
        self.student.save(update_fields=['pending_fees'])
        
        return pending_fees

    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(StudentYearlyFees, self.CODE_PREFIX)
        
        # Calculate pending fees before saving
        if not kwargs.get('update_fields'):
            self.calculate_pending_fees()
        
        super(StudentYearlyFees, self).save(*args, **kwargs)
        
    
class StoreCategory(CoreModel):
    name = models.CharField(max_length=100, unique=True)
    
    CODE_PREFIX = 'STCAT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(StoreCategory,'SCAT')
        super(StoreCategory, self).save(*args, **kwargs)
        
        
    def __str__(self):
        return self.name

class SubCategory(CoreModel):
    name = models.CharField(max_length=100)
    store_category = models.ForeignKey(StoreCategory, related_name='subcategories', on_delete=models.CASCADE)
    
    class Meta:
        unique_together = ('name', 'store_category')
    
    CODE_PREFIX = 'SCAT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(SubCategory,'SCAT')
        super(SubCategory, self).save(*args, **kwargs)
        
        
    def __str__(self):
        return f"{self.store_category.name} - {self.name}"

class InventoryItem(CoreModel):
    STATUS_CHOICES = [
        ('In Stock', 'In Stock'),
        ('Low Stock', 'Low Stock'),
        ('Out of Stock', 'Out of Stock'),
    ]
    
    name = models.CharField(max_length=200)
    store_category = models.ForeignKey(StoreCategory, related_name='items', on_delete=models.CASCADE)
    subcategory = models.ForeignKey(SubCategory, related_name='items', on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=0,blank=True, null=True)
    unit = models.CharField(max_length=50,blank=True, null=True)
    threshold = models.PositiveIntegerField(default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='In Stock')
    location = models.CharField(max_length=200,blank=True, null=True)
    supplier = models.CharField(max_length=200,blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2,blank=True, null=True)
    image = models.ImageField(upload_to='inventory_image/',blank=True, null=True,storage=MediaStorage())
    
    # seller_phone = models.CharField(max_length=15, blank=True, null=True)
    # def save(self, *args, **kwargs):
    #     if self.quantity <= 0:
    #         self.status = 'Out of Stock'
    #     elif self.quantity <= self.threshold:
    #         self.status = 'Low Stock'
    #     else:
    #         self.status = 'In Stock'
    #     super().save(*args, **kwargs)
    
    CODE_PREFIX = 'INVT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(InventoryItem,'INVT')
        super(InventoryItem, self).save(*args, **kwargs)
        
        
    def __str__(self):
        return self.name
    
    
    

class Miscellaneous(CoreModel):
    CATEGORY_CHOICES = [
        ('Bus', 'Bus'),
        ('Practical', 'Practical'),
        ('Exam', 'Exam'),
        ('Books', 'Books'),
        ('Building Fund', 'Building Fund'),
        ('Record', 'Record'),
        ('Other', 'Other'),
    ]
    
    PAYMENT_MODE_CHOICES = [
        ('cash', 'Cash'),
        ('upi', 'UPI'),
        ('card', 'Card'),
        ('cheque', 'Cheque'),
    ]

    student = models.ForeignKey('Student', related_name='miscellaneous_fees', on_delete=models.CASCADE)
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES)
    custom_category = models.CharField(max_length=100, blank=True, null=True, help_text="Required if category is 'Other'")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    paided_amount = models.DecimalField(max_digits=10, decimal_places=2)
    mis_pending_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)  # Auto-calculated
    receipt_no = models.CharField(max_length=20, unique=True, null=True, blank=True)
    payment_date = models.DateField(default=timezone.now)

    payment_mode = models.CharField(max_length=10, choices=PAYMENT_MODE_CHOICES, default='cash')
    # bank_account = models.CharField(max_length=100, null=True, blank=True)
    transaction_number = models.CharField(max_length=100, null=True, blank=True)
    bank_name = models.ForeignKey(BankName, on_delete=models.CASCADE, null=True, blank=True)
    
    def clean(self):
        from django.core.exceptions import ValidationError
        if self.category == 'Other' and not self.custom_category:
            raise ValidationError("Please specify the custom category for 'Other'.")

    def __str__(self):
        return f"{self.category} - {self.amount} for {self.student}"

    
    CODE_PREFIX = 'MISC'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code is None):
            self.code = getcode(Miscellaneous, 'MISC')

        if self.amount is not None and self.paided_amount is not None:
            self.mis_pending_amount = self.amount - self.paided_amount
        else:
            self.mis_pending_amount = 0


        if not self.receipt_no and self.student.academic_year:
            start_year = self.student.academic_year.start_date.year
            self.receipt_no = generate_receipt_no(start_year)


        super(Miscellaneous, self).save(*args, **kwargs)


        
        
    def __str__(self):
        return self.student.name