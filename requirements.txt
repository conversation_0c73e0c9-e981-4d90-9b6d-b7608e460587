asgiref==3.8.1
certifi==2025.4.26
charset-normalizer==3.4.2
contourpy==1.3.2
cycler==0.12.1
diff-match-patch==20241021
distlib==0.3.9
dj-database-url==2.3.0
Django==5.2.1
django-cors-headers==4.7.0
django-filter==25.1
django-import-export==4.3.7
djangorestframework==3.16.0
djangorestframework_simplejwt==5.5.0
drf-yasg==1.21.10
et_xmlfile==2.0.0
filelock==3.18.0
fonttools==4.58.0
gunicorn==23.0.0
idna==3.10
inflection==0.5.1
kiwisolver==1.4.8
matplotlib==3.10.3
mysqlclient==2.2.7
numpy==2.2.5
openpyxl==3.1.5
packaging==25.0
pandas==2.2.3
pillow==11.2.1
platformdirs==4.3.8
psycopg2-binary==2.9.10
PyJWT==2.9.0
pyparsing==3.2.3
PyPDF2==3.0.1
python-dateutil==2.9.0.post0
pytz==2025.2
PyYAML==6.0.2
requests==2.32.3
six==1.17.0
tablib==3.8.0
typing_extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.4.0
virtualenv==20.31.2
whitenoise==6.9.0

# MongoDB via mongoengine
pymongo
dnspython
mongoengine
# Celery + Redis
boto3==1.38.27
celery==5.3.6
django-celery-beat==2.8.1
django-celery-results==2.6.0
redis==5.0.3

# Caching, storage, misc
django-redis==5.4.0
django-debug-toolbar==5.2.0
django-storages==1.14.6
python-dotenv==1.1.0
xlrd==2.0.2