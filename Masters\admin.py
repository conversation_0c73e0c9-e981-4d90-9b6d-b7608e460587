from django.contrib import admin
from .models import BankName, Caste, Mis<PERSON>aneous, Student, AcademicYear, Class, Section, FeeTerm, Fees, Attendance, SubCaste, StudentYearlyFees, StudentTestResult, StudentTestSubjectMark
from django.urls import reverse
from django.utils.html import format_html

class AcademicYearAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'last_date')
    readonly_fields = ['code']
    search_fields = ('name',)
    ordering = ('-start_date',)

class ClassAdmin(admin.ModelAdmin):
    list_display = ('id','name',)
    search_fields = ('name',)
    ordering = ('name',)
    readonly_fields = ('code',)

class SectionAdmin(admin.ModelAdmin):
    list_display = ('id','name','class_name','group','batch','is_active')
    list_filter = ('class_name',)
    search_fields = ('name',)
    ordering = ('name',)
    readonly_fields = ('code',)

class StudentAdmin(admin.ModelAdmin):
    list_display = ('id','admission_no', 'phone_numbers','name', 'father_name', 'academic_year', 'class_name', 'section', 'status', 'date_of_admission', 'no_of_turns', 'initial_fee_paid', 'pending_fees','committed_fees','gender','is_join')
    search_fields = ('name', 'father_name', 'academic_year__name', 'class_name__name', 'section__name', 'status','admission_no')
    list_filter = ('status', 'academic_year', 'class_name', 'section')
    list_editable = ('status', 'no_of_turns', 'initial_fee_paid', 'is_join')
    readonly_fields = ('code','pending_fees',)
    list_per_page = 20

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related('academic_year', 'class_name', 'section')

    def save_model(self, request, obj, form, change):
        obj.calculate_pending_fees()
        super().save_model(request, obj, form, change)

class FeeTermAdmin(admin.ModelAdmin):
    list_display = ('id','code','student', 'academic_year', 'term', 'start_date', 'end_date', 'amount', 'is_paid')
    search_fields = ('student__name', 'academic_year__name')
    list_filter = ('academic_year', 'is_paid')
    ordering = ('student__name', 'term')
    readonly_fields = ('code',)

class FeesAdmin(admin.ModelAdmin):
    list_display = ('id','student', 'amount', 'payment_date', 'turn','academic_year')
    search_fields = ('student__name',)
    list_filter = ('payment_date',)
    ordering = ('-payment_date',)
    readonly_fields = ('code',)

class AttendanceAdmin(admin.ModelAdmin):
    list_display = ('student', 'date', 'is_present')
    search_fields = ('student__name',)
    list_filter = ('date', 'is_present')
    ordering = ('-date', 'student__name')
    readonly_fields = ('code',)

class TestMarksAdmin(admin.ModelAdmin):
    list_display = ('student', 'test_name', 'subject', 'marks_obtained', 'total_marks', 'test_date')
    search_fields = ('student__name', 'test_name', 'subject')
    list_filter = ('test_date',)
    ordering = ('-test_date', 'student__name')
    readonly_fields = ('code',)

class StudentYearlyFeesAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'student', 'academic_year', 'committed_fees', 'initial_fee_paid', 'pending_fees', 'is_active', 'created_on')
    list_filter = ('academic_year', 'is_active')
    search_fields = ('student__name', 'student__admission_no', 'academic_year__name')
    readonly_fields = ('code', 'pending_fees', 'created_on', 'modified_on')
    ordering = ('-academic_year__start_date', 'student__name')
    list_per_page = 20

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'academic_year')

    def save_model(self, request, obj, form, change):
        # Calculate pending fees before saving
        obj.pending_fees = obj.committed_fees - obj.initial_fee_paid
        super().save_model(request, obj, form, change)



from django.contrib import admin
from .models import StoreCategory, SubCategory, InventoryItem

class SubCategoryInline(admin.TabularInline):
    model = SubCategory
    extra = 1

# @admin.register(StoreCategory)
class StoreCategoryAdmin(admin.ModelAdmin):
    list_display = ['id','name']
    # inlines = [SubCategoryInline]
    readonly_fields = ['code']

# @admin.register(SubCategory)
class SubCategoryAdmin(admin.ModelAdmin):
    list_display = ['id','name', 'store_category']
    list_filter = ['store_category']
    readonly_fields = ['code']

# @admin.register(InventoryItem)
class InventoryItemAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'store_category', 'subcategory', 'quantity', 
        'unit', 'status', 'location', 'supplier','is_deleted'
    ]
    list_filter = ['store_category', 'subcategory', 'status']
    search_fields = ['name', 'location', 'supplier']
    readonly_fields = ['code']
    
class CasteAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name')
    search_fields = ('name', 'code')
    list_filter = ('name',)
    ordering = ('code',)
    readonly_fields = ('code',)

class SubCasteAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'caste')
    search_fields = ('name', 'code')
    list_filter = ('name',)
    ordering = ('code',)
    readonly_fields = ('code',)

class BankNameAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name')
    search_fields = ('name', 'code')
    list_filter = ('name',)
    ordering = ('code',)
    readonly_fields = ('code',)

class MiscellaneousAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'student', 'amount', 'payment_date', 'category', 'custom_category',
        'paided_amount', 'mis_pending_amount'
    )
    search_fields = ('student__name',)
    list_filter = ('payment_date',)
    ordering = ('-payment_date',)
    readonly_fields = ('code',)

    
# admin.site.register(TestMarks, TestMarksAdmin)  # Removed: TestMarks is deprecated
admin.site.register(StudentTestResult)
admin.site.register(StudentTestSubjectMark)
admin.site.register(Attendance, AttendanceAdmin)
admin.site.register(Fees, FeesAdmin)
admin.site.register(FeeTerm, FeeTermAdmin)
admin.site.register(Student, StudentAdmin)
admin.site.register(Section, SectionAdmin)
admin.site.register(Class, ClassAdmin)
admin.site.register(AcademicYear, AcademicYearAdmin)
admin.site.register(InventoryItem, InventoryItemAdmin)
admin.site.register(SubCategory, SubCategoryAdmin)
admin.site.register(StoreCategory, StoreCategoryAdmin)
admin.site.register(BankName, BankNameAdmin)
admin.site.register(Miscellaneous, MiscellaneousAdmin)
admin.site.register(Caste, CasteAdmin)
admin.site.register(SubCaste, SubCasteAdmin)
admin.site.register(StudentYearlyFees, StudentYearlyFeesAdmin)