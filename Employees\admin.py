from django.contrib import admin
from .models import Department, Category, Employee, EmployeeAttendance, Leave, Holiday, SalaryRecord, Expense
from django.urls import reverse
from django.utils.html import format_html

class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('id','name', 'description')
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ['code']

class CategoryAdmin(admin.ModelAdmin):
    list_display = ('id','name', 'description')
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ['code']

class EmployeeAdmin(admin.ModelAdmin):
    list_display = ('id','code','employee_no', 'name', 'email', 'phone', 'department', 'category', 'is_active')
    search_fields = ('name', 'email', 'phone', 'employee_no')
    list_filter = ('department', 'category', 'is_active')
    readonly_fields = ('employee_no',)
    ordering = ('name',)

class EmployeeAttendanceAdmin(admin.ModelAdmin):
    list_display = ('id','code','employee', 'date', 'is_present', 'check_in_time', 'check_out_time')
    list_filter = ('is_present', 'date')
    search_fields = ('employee__name', 'date')
    ordering = ('-date', 'employee__name')

class LeaveAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'start_date', 'end_date', 'is_approved')
    list_filter = ('leave_type', 'is_approved', 'start_date')
    search_fields = ('employee__name', 'reason')
    ordering = ('-start_date',)

class HolidayAdmin(admin.ModelAdmin):
    list_display = ('name', 'date', 'description')
    search_fields = ('name', 'description')
    list_filter = ('date',)
    ordering = ('-date',)

class SalaryRecordAdmin(admin.ModelAdmin):
    list_display = ('id','employee', 'payment_date','month', 'year', 'is_paid','basic_salary', 'net_salary','total_salary')
    list_filter = ('year', 'month')
    search_fields = ('employee__name',)
    ordering = ('-year', '-month')

class ExpenseAdmin(admin.ModelAdmin):
    list_display = ('name', 'quantity', 'price', 'transaction_id', 'seller_phone', 'date')
    search_fields = ('name', 'transaction_id', 'seller_phone')
    list_filter = ('date',)
    ordering = ('-date',)



admin.site.register(Expense, ExpenseAdmin)
admin.site.register(SalaryRecord, SalaryRecordAdmin)
admin.site.register(Holiday, HolidayAdmin)
admin.site.register(Leave, LeaveAdmin)
admin.site.register(EmployeeAttendance, EmployeeAttendanceAdmin)
admin.site.register(Employee, EmployeeAdmin)
admin.site.register(Category, CategoryAdmin)
admin.site.register(Department, DepartmentAdmin)

