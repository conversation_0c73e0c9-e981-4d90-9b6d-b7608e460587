import os
import requests


def normalize_number(number):
    number = str(number).strip()
    if number.startswith('+91'):
        return number[1:]
    elif number.startswith('91') and len(number) == 12:
        return number
    elif len(number) == 10:
        return '91' + number
    return None
class SpoorthiBulkSMSClient:


    def __init__(self):
        self.username = os.getenv('SMS_USERNAME')
        self.sendername = os.getenv('SMS_SENDER_NAME')
        self.smstype = 'TRANS'
        self.apikey = os.getenv('SMS_API_KEY')
        self.base_url = os.getenv('SMS_URL')

    def send_bulk_sms(self, numbers, message):
        try:
            # Clean and validate numbers
            cleaned_numbers = [normalize_number(num) for num in numbers if normalize_number(num)]
            if not cleaned_numbers:
                return {'status': 'error', 'message': 'No valid phone numbers provided', 'data': None}

            numbers_str = ','.join(cleaned_numbers)

            params = {
                'username': self.username,
                'message': message,
                'sendername': self.sendername,
                'smstype': self.smstype,
                'numbers': numbers_str,
                'apikey': self.apikey
            }

            # print("🔔 Sending SMS with params:", params)  # Debug log
            response = requests.get(self.base_url, params=params)
            # print("📩 SMS API response:", response.status_code, response.text)  # Debug log

            try:
                data = response.json()
            except Exception:
                data = response.text

            if response.status_code == 200:
                if isinstance(data, dict):
                    response_code = data.get('responseCode', '').lower()
                    status_field = data.get('status', '').lower()
                    if any(success in response_code or success in status_field for success in ['success', 'delivered', 'submitted']):
                        return {'status': 'success', 'message': 'SMS sent successfully', 'data': data}
                elif isinstance(data, list) and data:
                    response_code = data[0].get('responseCode', '').lower()
                    if any(success in response_code for success in ['success', 'delivered', 'submitted']):
                        return {'status': 'success', 'message': 'SMS sent successfully', 'data': data}

            return {'status': 'error', 'message': f'Failed to send SMS: {data}', 'data': data}
        except Exception as e:
            return {'status': 'error', 'message': f'Error sending SMS: {str(e)}', 'data': None}
