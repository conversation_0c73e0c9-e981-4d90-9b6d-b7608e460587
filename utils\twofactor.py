import requests
class TwoFactorClient:
    def __init__(self):
        self.api_key = '14f71072-4484-11f0-a562-0200cd936042'
        self.url = 'https://2factor.in/API/V1/{api_key}/ADDON_SERVICES/SEND/TSMS'
        self.sender_id = 'SJCMsg'
        self.template_name = 'Spoorthi'  # Your approved template

    def send_bulk_sms(self, phone_numbers, template_vars):
        valid_numbers = [num for num in phone_numbers if num and len(num) == 10]
        if not valid_numbers:
            return {"status": "error", "message": "No valid phone numbers found"}

        mobile_string = ','.join(valid_numbers)

        # Build the payload with template variables
        payload = {
            'From': self.sender_id,
            'To': mobile_string,
            'TemplateName': self.template_name
        }

        # Add the template variables to the payload
        # template_vars is a dict like {'VAR1': 'John', 'VAR2': 'Term 1', 'VAR3': '1500'}
        payload.update(template_vars)

        try:
            print("Payload being sent:", payload)  # Debug
            res = requests.post(self.url.format(api_key=self.api_key), data=payload)
            print("Raw response:", res.text)  # Debug

            res_json = res.json()

            if res.status_code == 200 and res_json.get("Status") == "Success":
                return {"status": "success", "data": res_json}
            else:
                return {"status": "error", "message": res_json.get("Details", "Unknown error")}
        except Exception as e:
            return {"status": "error", "message": str(e)}
