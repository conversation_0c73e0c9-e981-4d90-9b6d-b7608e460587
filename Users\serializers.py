import re
from django.forms import ValidationError
from rest_framework import serializers

from .models import User
from rest_framework.validators import UniqueValidator
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError




def validate_pwd(value):

    if len(value) < 8:
        raise ValidationError("Password must be at least 8 characters long.")
    
    if not value[0].isupper():
        raise ValidationError("Password must start with a capital letter.")
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', value):
        raise ValidationError("Password must contain at least one special character.")
        
    return value


class UserMiniSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = User
        fields = ['id','email']
        
        
        
class UserSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        read_only= ('id')
        fields = ['id', 'email', 'is_active', 'is_admin', 'is_employee', 'is_director']
        extra_kwargs = {
            'email': {'required': False},
        }
        
        
class RegisterSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(required=True,validators=[UniqueValidator(queryset=get_user_model().objects.all(), message="Email already exists.")])    
    password = serializers.CharField( write_only=True,required=True,validators=[validate_pwd])
    # password2 = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        read_only= ('id')
        fields = ('id','email', 'password')

    # def validate(self, attrs):
    #     if attrs['password'] != attrs['password2']:
    #         raise serializers.ValidationError({"password": "Password fields didn't match."})
    #     return attrs

    def create(self, validated_data):
        # Check if email already exists manually (for any reason, custom error handling)
        if get_user_model().objects.filter(email=validated_data['email']).exists():
            raise ValidationError({"email": "Email already exists."})

        user = get_user_model().objects.create_user(
            email=validated_data['email'],
            password=validated_data['password']
        )
        user.is_active = True  # Ensure the user is active
        user.save()
        return user



class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate(self, data):
        email = data['email']
        password = data['password']

        User = get_user_model()
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid email or password")

        if not user.check_password(password):
            raise serializers.ValidationError("Invalid email or password")
        
        return {'user': user}
    
    

class TokenRefreshSerializer(serializers.Serializer):
    refresh = serializers.CharField()

    def validate(self, data):
        try:
            refresh = RefreshToken(data['refresh'])
            return {
                'access': str(refresh.access_token)
            }
        except Exception as e:
            raise serializers.ValidationError("Invalid refresh token.")



class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_new_password = serializers.CharField(write_only=True)

    def validate(self, data):
        if data['new_password'] != data['confirm_new_password']:
            raise serializers.ValidationError("The new passwords must match.")
        return data




