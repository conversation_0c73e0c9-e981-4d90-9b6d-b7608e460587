import uuid
from rest_framework import generics, status, viewsets
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import action
import calendar
import numpy as np

from Employees.models import Holiday
from utils.twofactor import TwoFactorClient
from utils.msg91 import SpoorthiBulkSMSClient
from .models import AcademicYear, Caste, Class, Miscellaneous, Section, Student, FeeTerm, Fees, Attendance, SubCaste, StudentYearlyFees, BankName
from .serializers import AcademicYearSerializer, BulkStudentMessageSerializer, BulkStudentUploadSerializer, CasteSerializer, ClassSerializer, EducationalOfficerSerializer, MiscellaneousSerializer, SectionSerializer, StudentSectionSerializer,StudentSerializer, FeeTermSerializer, FeesSerializer, AttendanceSerializer, SubCasteSerializer,TermPendingFeesSerializer, InitialStudentSerializer,BulkAttendanceUploadSerializer, BulkTestMarksUploadSerializer, SectionDetailSerializer, StudentYearlyFeesSerializer, BankNameSerializer, StudentTestResultSerializer
from .models import StudentTestResult, StudentTestSubjectMark

import pandas as pd
from django.db import transaction
from datetime import datetime, timedelta
from django.db.models import Sum, Count, Avg
from django.db.models.functions import ExtractMonth, ExtractYear
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from django.http import Http404
from rest_framework.exceptions import ValidationError
from django.utils.timezone import now
# from django.db.models import Sum, Q
# from datetime import datetime, timedelta
from django_filters.rest_framework import FilterSet
from django_filters import DateRangeFilter,DateFilter
from django.core.cache import cache
from .tasks import calculate_student_pending_fees, update_student_yearly_fees, update_attendance_cache
from spoorthi_crm.decorators import cache_response, invalidate_cache_pattern
import time
# from utils.pagination import CustomPagination 
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from utils.msg91 import SpoorthiBulkSMSClient

class AcademicYearListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Academic years retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Academic year created successfully")
        return self.get_error_response("Error creating academic year", serializer.errors)

class AcademicYearDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Academic year retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Academic year updated successfully")
        return self.get_error_response("Error updating academic year", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Academic year deleted successfully")

# --------- Class Views ---------
class ClassListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Class.objects.all()
    serializer_class = ClassSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Classes retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Class created successfully")
        return self.get_error_response("Error creating class", serializer.errors)

class ClassDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Class.objects.all()
    serializer_class = ClassSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Class retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Class updated successfully")
        return self.get_error_response("Error updating class", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Class deleted successfully")

# --------- Section Views ---------
class SectionListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Section.objects.all()
    serializer_class = SectionSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Sections retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Section created successfully")
        return self.get_error_response("Error creating section", serializer.errors)

class SectionDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Section.objects.all()
    serializer_class = SectionSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Section retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Section updated successfully")
        return self.get_error_response("Error updating section", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Section deleted successfully")

# --------- Student Views ---------
from rest_framework import filters
from django_filters.rest_framework import DjangoFilterBackend


class StudentFilter(FilterSet):
    date_range = DateRangeFilter(field_name='date')
    start_date = DateFilter(field_name='date',lookup_expr=('gte'),)
    end_date = DateFilter(field_name='date',lookup_expr=('lte'))

    class Meta:
        model = Student
        fields = ['admission_no', 'name',]


class StudentListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Student.objects.select_related('academic_year', 'class_name', 'section').prefetch_related('feeterm_set', 'fees_set').all()
    filter_backends = [filters.SearchFilter]
    filterset_class = StudentFilter
    search_fields = ['admission_no', 'name']

    def get_serializer_class(self):
        return InitialStudentSerializer if self.request.method == 'POST' else StudentSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False).order_by('-code')

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        # paginator = CustomPagination()
        # paginated_qs = paginator.paginate_queryset(queryset, request)

        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Students retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            student_instance = serializer.save(created_by=self.request.user)
            
            student_instance.refresh_from_db()
            
            response_serializer = StudentSerializer(student_instance)
            return self.get_success_response(response_serializer.data, "Student created successfully")
        return self.get_error_response("Error creating student", serializer.errors)

class StudentDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Student.objects.select_related(
        'academic_year', 'class_name', 'section'
    ).prefetch_related(
        'feeterm_set', 'fees_set'
    ).all()
    serializer_class = StudentSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Student retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Student updated successfully")
        return self.get_error_response("Error updating student", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Student deleted successfully")

# --------- FeeTerm Views ---------
class FeeTermListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = FeeTerm.objects.select_related('student').all()
    serializer_class = FeeTermSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Fee terms retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Fee term created successfully")
        return self.get_error_response("Error creating fee term", serializer.errors)

class FeeTermDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = FeeTerm.objects.select_related('student').all()
    serializer_class = FeeTermSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Fee term retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Fee term updated successfully")
        return self.get_error_response("Error updating fee term", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Fee term deleted successfully")

# --------- Fees Views ---------

class FeesFilter(FilterSet):
    date_range = DateRangeFilter(field_name='date')
    start_date = DateFilter(field_name='date',lookup_expr=('gte'),)
    end_date = DateFilter(field_name='date',lookup_expr=('lte'))

    class Meta:
        model = Fees
        fields = ['student__name', 'payment_date']
        
        
class FeesListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Fees.objects.select_related('student').all()
    serializer_class = FeesSerializer
    filterset_class = FeesFilter
    filter_backends = [filters.SearchFilter, DjangoFilterBackend,]
    search_fields = ['student__name', 'payment_date']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Fees retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            student = serializer.validated_data['student']
            payment_date = serializer.validated_data.get('payment_date', timezone.now().date())
            
            # Check if student's admission date is before their academic year
            if student.date_of_admission < student.academic_year.start_date:
                # Check for pending fees in previous academic years
                previous_yearly_fees = StudentYearlyFees.objects.filter(
                    student=student,
                    academic_year__start_date__lt=student.academic_year.start_date,
                    is_active=True
                ).order_by('-academic_year__start_date')
                
                if previous_yearly_fees.exists():
                    # If there are pending fees in previous years, use that academic year
                    academic_year = previous_yearly_fees.first().academic_year
                else:
                    # If no pending fees, use student's current academic year
                    academic_year = student.academic_year
            else:
                # If admission date is after academic year start, use current academic year
                academic_year = student.academic_year
            
            # Check if there are any pending fee terms for the academic year
            pending_terms = FeeTerm.objects.filter(
                student=student,
                academic_year=academic_year,
                is_paid=False
            ).exists()
            
            if not pending_terms:
                return self.get_error_response(
                    "No pending fee terms found for the selected academic year",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            # Create the fee payment
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Fee created successfully")
        return self.get_error_response("Error creating fee", serializer.errors)

class FeesDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Fees.objects.select_related('student').all()
    serializer_class = FeesSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Fee retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)  # Allow partial update
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Fee updated successfully")
        return self.get_error_response("Error updating fee", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Fee deleted successfully")

# --------- Attendance Views ---------
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters

class AttendanceFilter(FilterSet):
    # date_range = DateRangeFilter(field_name='date')
    start_date = DateFilter(field_name='date',lookup_expr=('gte'),)
    end_date = DateFilter(field_name='date',lookup_expr=('lte'))

    class Meta:
        model = Attendance
        fields = ['student', 'start_date', 'end_date']


class AttendanceListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Attendance.objects.select_related('student').filter(student__is_join=True,is_deleted=False).all()
    serializer_class = AttendanceSerializer
    filterset_class = AttendanceFilter
    filter_backends = [DjangoFilterBackend]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    # def get_queryset(self):
    #     queryset = super().get_queryset()
    #     date = self.request.query_params.get('date')
    #     student_id = self.request.query_params.get('student_id')

    #     if date:
    #         queryset = queryset.filter(date=date)
    #     if student_id:
    #         queryset = queryset.filter(student_id=student_id)

        # return queryset

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())  # <-- Use filter_queryset
        print("Filtered queryset:", queryset.query)
        if not queryset.exists():
            return self.get_success_response([], "No attendance records found")

        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Attendance records retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Attendance record created successfully")
        return self.get_error_response("Error creating attendance record", serializer.errors)

class AttendanceDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Attendance.objects.select_related('student').all()
    serializer_class = AttendanceSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Attendance record retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Attendance record updated successfully")
        return self.get_error_response("Error updating attendance record", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Attendance record deleted successfully")

from rest_framework import filters
from django_filters.rest_framework import DjangoFilterBackend, FilterSet

class StudentTermPendingFeesView(APIView):
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ['code']
    ordering_fields = ['code']
    
    def get(self, request, pk):
        try:
            # Convert string UUID to UUID object if needed
            try:
                student_id = uuid.UUID(str(pk))
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid student ID format'
                }, status=400)

            # Get the student with proper error handling
            student = Student.objects.get(id=student_id)
            
            # Check if today is the last date of the academic year
            today = timezone.now().date()
            is_last_day = (
                student.academic_year and 
                student.academic_year.last_date and 
                today == student.academic_year.last_date
            )

            is_in_current_year = (
                student.academic_year and 
                student.date_of_admission >= student.academic_year.start_date and 
                student.date_of_admission <= student.academic_year.last_date
            )
            
            initial_fee_amount = float(student.initial_fee_paid) if student.initial_fee_paid else 0
            
                # Generate fee terms if they don't exist
            student.generate_fee_terms()
            
            # Get fee terms
            fee_terms = FeeTerm.objects.filter(
                student=student,
                academic_year=student.academic_year
            ).order_by('term')
            
            # Get fee payments
            fee_payments = Fees.objects.filter(
                student=student,
                payment_date__gte=student.academic_year.start_date,
                payment_date__lte=student.academic_year.last_date
            ).order_by('payment_date')
            
            total_fees_paid = fee_payments.aggregate(total=Sum('amount'))['total'] or 0
            total_fees_paid = float(total_fees_paid)
            total_paid = total_fees_paid + initial_fee_amount
            
            # Reset all term paid amounts and statuses
            FeeTerm.objects.filter(
                student=student,
                academic_year=student.academic_year
            ).update(paid_amount=0, is_paid=False)
            
            # Refresh the terms after update
            fee_terms = FeeTerm.objects.filter(
                student=student,
                academic_year=student.academic_year
            ).order_by('term')
            
            # Allocate initial fee payment to terms
            remaining_initial = initial_fee_amount
            for term in fee_terms:
                if remaining_initial <= 0:
                    break
                    
                term_pending = float(term.amount) - float(term.paid_amount)
                allocate_amount = min(remaining_initial, term_pending)
                
                if allocate_amount > 0:
                    term.paid_amount = float(term.paid_amount) + allocate_amount
                    remaining_initial -= allocate_amount
                    
                    # Update is_paid status
                    term.is_paid = (float(term.paid_amount) >= float(term.amount))
                    term.save()
            
            # Allocate fee payments to remaining term amounts
            remaining_fees = total_fees_paid
            for term in fee_terms:
                if remaining_fees <= 0:
                    break
                    
                term_pending = float(term.amount) - float(term.paid_amount)
                allocate_amount = min(remaining_fees, term_pending)
                
                if allocate_amount > 0:
                    term.paid_amount = float(term.paid_amount) + allocate_amount
                    remaining_fees -= allocate_amount
                    
                    # Update is_paid status
                    term.is_paid = (float(term.paid_amount) >= float(term.amount))
                    term.save()
            
            # Calculate final pending fees
            calculated_pending_fees = float(student.committed_fees) - total_paid
            
            # Update yearly fees record
            yearly_fees = StudentYearlyFees.objects.filter(
                student=student,
                academic_year=student.academic_year,
                is_active=True
            ).first()
            
            if yearly_fees:
                yearly_fees.pending_fees = calculated_pending_fees
                yearly_fees.save()
            
            # Update student's pending fees
            student.pending_fees = calculated_pending_fees
            student.save()
            
            # Refresh terms for response
            fee_terms = FeeTerm.objects.filter(
                student=student,
                academic_year=student.academic_year
            ).order_by('term')
            
            terms_data = []
            for term in fee_terms:
                terms_data.append({
                    'term': term.term,
                    'amount': float(term.amount),
                    'paid_amount': float(term.paid_amount),
                    'pending_amount': float(term.amount) - float(term.paid_amount),
                    'is_paid': term.is_paid,
                    'start_date': term.start_date,
                    'end_date': term.end_date
                })
            
            fee_payment_details = [{
                'amount': float(payment.amount),
                'payment_date': payment.payment_date,
                'turn': payment.turn
            } for payment in fee_payments]
            
            return Response({
                'status': 'success',
                'message': 'Fee terms retrieved successfully',
                'data': {
                    'student_id': student.id,
                    'student_name': student.name,
                    'academic_year': student.academic_year.name if student.academic_year else None,
                    'date_of_admission': student.date_of_admission,
                    'is_in_current_year': is_in_current_year,
                    'committed_fees': float(student.committed_fees),
                    'initial_fee_paid': float(student.initial_fee_paid),
                    'pending_fees': calculated_pending_fees,
                    'total_paid': total_paid,
                    'fee_payments': fee_payment_details,
                    'terms': terms_data
                }
            })
            
        except Student.DoesNotExist:
            return Response({
                'status': 'error',
                'message': f'Student with ID {pk} not found'
            }, status=404)
            
        except ValueError as ve:
            return Response({
                'status': 'error',
                'message': f'Invalid data format: {str(ve)}'
            }, status=400)
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response({
                'status': 'error',
                'message': f'Error retrieving fee terms: {str(e)}'
            }, status=500)

# from django.utils.decorators import method_decorator
# from django.views.decorators.csrf import csrf_exempt
# @method_decorator(csrf_exempt, name='dispatch')
class BulkAttendanceUploadView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = BulkAttendanceUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        file = serializer.validated_data['file']

        try:
            df = pd.read_excel(file)

            required_columns = ['admission_no', 'date', 'attendance_status']
            if not all(col in df.columns for col in required_columns):
                return Response({
                    "error": f"Excel file must contain columns: {', '.join(required_columns)}"
                }, status=status.HTTP_400_BAD_REQUEST)

            success_count = 0
            errors = []

            with transaction.atomic():
                for index, row in df.iterrows():
                    row_num = index + 2  # Excel rows start at 1 and header is row 1
                    try:
                        admission_no = str(row['admission_no']).strip()
                        date = pd.to_datetime(row['date']).date()
                        attendance_status = str(row['attendance_status']).strip().lower()

                        # Validate date
                        if date > timezone.now().date():
                            errors.append({
                                'row': row_num,
                                'error': f"Date {date} cannot be in the future"
                            })
                            continue

                        # Normalize attendance status
                        if attendance_status in ['present', 'p']:
                            is_present = True
                        elif attendance_status in ['absent', 'a']:
                            is_present = False
                        else:
                            errors.append({
                                'row': row_num,
                                'error': f"Invalid attendance status: {attendance_status}. Must be 'Present' or 'Absent'"
                            })
                            continue

                        # Get student
                        student = Student.objects.get(admission_no=admission_no)

                        # Update or create attendance record
                        attendance, created = Attendance.objects.update_or_create(student=student,date=date,defaults={'is_present': is_present})

                        success_count += 1

                    except Student.DoesNotExist:
                        errors.append({
                            'row': row_num,
                            'error': f"Student with admission number {admission_no} not found"
                        })
                    except ValueError as e:
                        errors.append({
                            'row': row_num,
                            'error': f"Invalid data in row: {str(e)}"
                        })
                    except Exception as e:
                        errors.append({
                            'row': row_num,
                            'error': f"Error processing row: {str(e)}"
                        })

            return Response({
                "status": "success",
                "message": f"Successfully processed {success_count} attendance records",
                "errors": errors if errors else None
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Error processing file: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)

class StudentAttendanceFilter(FilterSet):
    date_range = DateRangeFilter(field_name='date')
    start_date = DateFilter(field_name='date', lookup_expr='gte')
    end_date = DateFilter(field_name='date', lookup_expr='lte')

    class Meta:
        model = Attendance
        fields = ['student', 'date_range', 'start_date', 'end_date']
        
class StudentAttendanceView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = StudentAttendanceFilter
    serializer_class = AttendanceSerializer

    def get_queryset(self):
        student_id = self.kwargs.get('student_id')
        try:
            student = Student.objects.get(id=student_id, is_join=True)
            return Attendance.objects.filter(student=student).order_by('date')
        except Student.DoesNotExist:
            return Attendance.objects.none()

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        if not queryset.exists():
            return Response({
                "status": "success",
                "message": "No attendance records found for the given filters",
                "data": []
            })

        student = queryset.first().student
        start_date = queryset.first().date
        end_date = queryset.last().date

        holidays = Holiday.objects.filter(date__range=[start_date, end_date])
        holiday_dates = {h.date for h in holidays}
        attendance_map = {a.date: a for a in queryset}

        calendar_data = []
        current_date = start_date
        while current_date <= end_date:
            attendance = attendance_map.get(current_date)
            calendar_data.append({
                "date": current_date,
                "is_holiday": current_date in holiday_dates,
                "is_weekend": current_date.weekday() >= 5,
                "is_present": attendance.is_present if attendance else None,
                "attendance_id": attendance.id if attendance else None,
            })
            current_date += timedelta(days=1)

        return Response({
            "status": "success",
            "message": "Student attendance records retrieved successfully",
            "student_name": student.name,
            "data": calendar_data
        })


class TestMarksListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = StudentTestResult.objects.select_related('student').prefetch_related('subject_marks').all()
    serializer_class = StudentTestResultSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.query_params.get('student_id')
        test_name = self.request.query_params.get('test_name')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if test_name:
            queryset = queryset.filter(test_name__icontains=test_name)
        if start_date:
            queryset = queryset.filter(test_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(test_date__lte=end_date)

        return queryset

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        if not queryset.exists():
            return Response({
                "status": "success",
                "message": "No test marks found",
                "data": []
            }, status=status.HTTP_200_OK)

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            "status": "success",
            "message": "Test marks retrieved successfully",
            "data": serializer.data
        })


class TestMarksDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = StudentTestResult.objects.select_related('student').prefetch_related('subject_marks').all()
    serializer_class = StudentTestResultSerializer

    def get_queryset(self):
        return super().get_queryset()

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return self.get_success_response(serializer.data, "Test result retrieved successfully")
        except StudentTestResult.DoesNotExist:
            return self.get_error_response("Test result not found", status_code=status.HTTP_404_NOT_FOUND)

    def put(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Test result updated successfully")
        except StudentTestResult.DoesNotExist:
            return self.get_error_response("Test result not found", status_code=status.HTTP_404_NOT_FOUND)

    def delete(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return self.get_success_response(None, "Test result deleted successfully")
        except StudentTestResult.DoesNotExist:
            return self.get_error_response("Test result not found", status_code=status.HTTP_404_NOT_FOUND)


class StudentTestMarksView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, student_id):
        try:
            test_results = StudentTestResult.objects.filter(student_id=student_id).order_by('-test_date').prefetch_related('subject_marks')
            if not test_results.exists():
                return Response({
                    "status": "success",
                    "message": "No test marks found",
                    "data": []
                })
            serializer = StudentTestResultSerializer(test_results, many=True)
            return Response({
                "status": "success",
                "message": "Student test marks retrieved successfully",
                "data": serializer.data
            })
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Error retrieving test marks: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)


class BulkTestMarksUploadView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({"status": "error", "message": "No file uploaded"}, status=status.HTTP_400_BAD_REQUEST)
        test_name = file.name.rsplit('.', 1)[0]
        try:
            df = pd.read_excel(file)
        except Exception as e:
            return Response({"status": "error", "message": f"Error reading Excel file: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

        subject_columns = []
        for col in df.columns:
            if col.endswith('_Marks') and not col.startswith('Total'):
                subject = col.replace('_Marks', '')
                subject_columns.append(subject)

        errors = []
        success_count = 0
        with transaction.atomic():
            for index, row in df.iterrows():
                row_num = index + 2
                try:
                    omr_number = str(row['OMR NUMBER']).strip()
                    student = Student.objects.filter(admission_no__iexact=omr_number).first()
                    if not student:
                        errors.append({'row': row_num, 'error': f'Student with OMR number (admission_no) "{omr_number}" not found'})
                        continue
                    section = row.get('SECTION', None)
                    test_date = None  # Optionally parse from request or Excel if available

                    # Use 'Grand Total Marks' for test result total_marks
                    total_marks = float(row.get('Grand Total Marks', 0))
                    overall_rank = int(row.get('Rank', 0)) if not pd.isna(row.get('Rank', None)) else None

                    subject_marks = []
                    for subject in subject_columns:
                        marks_col = f'{subject}_Marks'
                        rank_col = f'{subject}_Rank'
                        marks_obtained = float(row.get(marks_col, 0))
                        subj_rank = int(row.get(rank_col, 0)) if not pd.isna(row.get(rank_col, None)) else None

                        # Find the correct total column for this subject
                        # Try to match exactly as in the Excel: 'Total Maths_Marks', 'Total_ Physics_Marks', etc.
                        possible_total_cols = [
                            f'Total {subject}_Marks',
                            f'Total_{subject}_Marks',
                            f'Total {subject} Marks',
                            f'Total_{subject} Marks',
                            f'Total{subject}_Marks',
                            f'Total{subject}Marks',
                        ]
                        subj_total_marks = None
                        for col in possible_total_cols:
                            if col in row and not pd.isna(row.get(col)):
                                subj_total_marks = float(row.get(col, 0))
                                break
                        # If still not found, try to match ignoring spaces/underscores
                        if subj_total_marks is None:
                            for col in row.keys():
                                if col.replace(' ', '').replace('_', '').lower() == f'total{subject}marks'.replace(' ', '').replace('_', '').lower():
                                    if not pd.isna(row.get(col)):
                                        subj_total_marks = float(row.get(col, 0))
                                        break
                        if subj_total_marks is None:
                            # fallback: use test total_marks
                            subj_total_marks = total_marks

                        subject_marks.append({
                            'subject': subject,
                            'marks_obtained': marks_obtained,
                            'total_marks': subj_total_marks,
                            'rank': subj_rank
                        })

                    test_result, created = StudentTestResult.objects.update_or_create(
                        student=student,
                        test_name=test_name,
                        defaults={
                            'test_date': test_date or timezone.now().date(),
                            'total_marks': total_marks,
                            'rank': overall_rank
                        }
                    )
                    test_result.subject_marks.all().delete()
                    for sm in subject_marks:
                        StudentTestSubjectMark.objects.create(test_result=test_result, **sm)
                    success_count += 1
                except Exception as e:
                    errors.append({'row': row_num, 'error': str(e)})
        return Response({
            "status": "success",
            "message": f"Successfully processed {success_count} test results.",
            "errors": errors if errors else None
        }, status=status.HTTP_201_CREATED)


class FeeCollectionDetailsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            academic_year = request.query_params.get('academic_year')
            
            # Base queryset for fees
            fees_queryset = Fees.objects.filter(student__is_join=True, student__is_deleted=False)
            students_queryset = Student.objects.filter(is_join=True, is_deleted=False)
            
            if academic_year:
                fees_queryset = fees_queryset.filter(student__academic_year__name=academic_year)
                students_queryset = students_queryset.filter(academic_year__name=academic_year)

            # Get total initial fees paid
            total_initial = students_queryset.aggregate(
                total=Sum('initial_fee_paid')
            )['total'] or 0

            # Get total fees collected (excluding initial fees)
            total_collected = fees_queryset.aggregate(
                total=Sum('amount')
            )['total'] or 0

            # Get total fees collected (including initial fees)
            total_fees_collected = total_initial + total_collected

            total_committed_fees = students_queryset.aggregate(
                total=Sum('committed_fees')
            )['total'] or 0
            total_pending_fees = total_committed_fees - total_fees_collected

            today = datetime.now().date()
            current_month = today.month
            current_year = today.year
            
            # Calculate exact 3-month period (current month and previous two months)
            three_months_data = []
            three_month_total = 0
            
            for i in range(3):
                month = current_month - i
                year = current_year
                if month < 1:
                    month += 12
                    year -= 1
                
                month_fees = fees_queryset.filter(
                    payment_date__month=month,
                    payment_date__year=year
                ).aggregate(total=Sum('amount'))['total'] or 0
                
                three_months_data.append({
                    'month': datetime(year, month, 1).strftime('%B %Y'),
                    'amount': float(month_fees)
                })
                three_month_total += month_fees

            # Get monthly fee collection for the academic year
            monthly_collection = fees_queryset.annotate(
                month=ExtractMonth('payment_date'),
                year=ExtractYear('payment_date')
            ).values('month', 'year').annotate(
                total=Sum('amount')
            ).order_by('year', 'month')

            # Format monthly collection data
            monthly_data = []
            for entry in monthly_collection:
                month_name = datetime(entry['year'], entry['month'], 1).strftime('%B')
                monthly_data.append({
                    'month': month_name,
                    'year': entry['year'],
                    'total': float(entry['total'])
                })

            # Get last 5 fee payments
            last_payments = fees_queryset.select_related('student').order_by('-payment_date', '-created_on')[:5]
            last_payment_details = [{
                'student_name': payment.student.name,
                'amount': float(payment.amount),
                'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
                'turn': payment.turn
            } for payment in last_payments]

            # Get academic year-wise total collection
            academic_year_collection = Student.objects.values(
                'academic_year__name'
            ).annotate(
                initial_fees=Sum('initial_fee_paid'),
                term_fees=Sum('fees__amount')
            ).order_by('academic_year__name')

            academic_year_data = []
            for entry in academic_year_collection:
                if entry['academic_year__name']:  # Skip if academic year is None
                    total = (entry['initial_fees'] or 0) + (entry['term_fees'] or 0)
                    academic_year_data.append({
                        'academic_year': entry['academic_year__name'],
                        'total_collection': float(total)
                    })

            response_data = {
                'total_fees_collected': float(total_fees_collected),
                'total_pending_fees': float(total_pending_fees),
                'three_month_revenue': {
                    'total': float(three_month_total),
                    'months': three_months_data
                },
                'yearly_revenue': float(total_collected),  # Total for current year (excluding initial fees)
                'monthly_collection': monthly_data,
                'academic_year_collection': academic_year_data,
                'last_payments': last_payment_details
            }

            return Response({
                'status': 'success',
                'message': 'Fee collection details retrieved successfully',
                'data': response_data
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error retrieving fee collection details: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class AbsentStudentsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, date):
        try:
            # Get query parameters for filtering
            name = request.query_params.get('name', '')
            class_name = request.query_params.get('class', '')
            section = request.query_params.get('section', '')
            group = request.query_params.get('group', '')

            # Get all active students
            all_students = Student.objects.filter(is_join=True, is_deleted=False).select_related('class_name', 'section')
            
            # Get students with attendance records for the date
            students_with_attendance = Student.objects.filter(
                attendance__date=date,
                is_join=True,
                is_deleted=False
            ).select_related('class_name', 'section').distinct()
            
            # Get present and absent students
            present_students = students_with_attendance.filter(
                attendance__date=date,
                attendance__is_present=True
            ).distinct()
            
            absent_students = students_with_attendance.filter(
                attendance__date=date,
                attendance__is_present=False
            ).distinct()
            
            present_count = present_students.count()
            absent_count = absent_students.count()
            
            # Total students who have attendance records
            total_with_attendance = present_count + absent_count
            
            # Total active students
            total_students = all_students.count()

            # Apply filters to absent students
            if name:
                absent_students = absent_students.filter(name__icontains=name)
            if class_name:
                absent_students = absent_students.filter(class_name__name__icontains=class_name)
            if section:
                absent_students = absent_students.filter(section__name__icontains=section)
            if group:
                absent_students = absent_students.filter(group__icontains=group)

            # Order by name
            absent_students = absent_students.order_by('name')

            # Prepare response data
            response_data = {
                'date': date,
                'attendance_summary': {
                    'total_students': total_students,
                    'total_with_attendance': total_with_attendance,
                    'total_present': present_count,
                    'total_absent': absent_count
                },
                'absent_students': [{
                    'id': student.id,
                    'name': student.name,
                    'phone_numbers': student.phone_numbers,
                    'class': student.class_name.name if student.class_name else None,
                    'section': student.section.name if student.section else None,
                    'group': student.group,
                    'batch': student.batch
                } for student in absent_students]
            }

            return Response({
                'status': 'success',
                'message': f'Found {absent_count} absent students out of {total_with_attendance} students with attendance records (Total active students: {total_students})',
                'data': response_data
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error retrieving absent students: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


class BulkStudentMessageView(generics.CreateAPIView):
    permission_classes = [AllowAny]       
    serializer_class = BulkStudentMessageSerializer

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def post(self, request):
        try:
            # For marks alert, expect variables in request.data
            message_type = request.data.get('type', 'marks')
            sms_client = SpoorthiBulkSMSClient()
            students = Student.objects.filter(
                phone_numbers__isnull=False,
                is_join=True,
                is_deleted=False
            ).exclude(phone_numbers='')

            if not students.exists():
                return self.get_error_response('No students found with valid phone numbers', status_code=status.HTTP_404_NOT_FOUND)

            phone_numbers = []
            for student in students:
                pn = student.phone_numbers
                phone = get_first_phone_number(pn)
                if phone:
                    phone_numbers.append(phone)
            phone_numbers = list(set(filter(None, phone_numbers)))

            if not phone_numbers:
                return self.get_error_response('No valid phone numbers found', status_code=status.HTTP_404_NOT_FOUND)

            # Message templates
            if message_type == 'marks':
                # Example expects: student_name, exam_name, subject1, subject2, subject3, subject4, total
                # You may need to loop per student for personalized messages
                results = []
                for student in students:
                    # You may want to fetch marks data here
                    # For now, use placeholders
                    message = (
                        f"Dear Parent, Your ward {student.name} , Marks of Exam: EXAM, Subjects: S1, S2, S3, S4, Total:- TOTAL . Spoorthi Junior College"
                    )
                    phone = get_first_phone_number(student.phone_numbers)
                    if not phone:
                        continue
                    result = sms_client.send_bulk_sms([phone], message)
                    results.append({
                        'student': student.name,
                        'phone': str(phone),
                        'message': message,
                        'result': result
                    })
                return self.get_success_response({
                    'results': results
                }, f'Marks alert messages sent to {len(results)} students')
            else:
                # fallback: send provided message to all
                message = request.data.get('message')
                result = sms_client.send_bulk_sms(phone_numbers, message)
                if result['status'] == 'success':
                    return self.get_success_response({
                        'total_recipients': len(phone_numbers),
                        'message': message,
                        'response': result['data']
                    }, f'Message sent successfully to {len(phone_numbers)} students')
                else:
                    return self.get_error_response(result['message'])
        except Exception as e:
            return self.get_error_response(f'Error sending messages: {str(e)}')


class BulkAbsentStudentMessageView(APIView):
    permission_classes = [IsAuthenticated]

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def post(self, request, date=None):
        try:
            if not date:
                date = timezone.now().date().strftime('%Y-%m-%d')
            sms_client = SpoorthiBulkSMSClient()
            base_message = (
                "Dear Parent, Your Ward {student_name} was absent today. Regular attendance is key to progress. Spoorthi Junior College"
            )
            absent_students = Student.objects.filter(
                attendance__date=date,
                attendance__is_present=False,
                is_join=True,
                is_deleted=False
            ).select_related('class_name', 'section')
            if not absent_students.exists():
                return self.get_error_response(f'No absent students found for date {date}', status_code=status.HTTP_404_NOT_FOUND)
            student_details = []
            messages_sent = []
            failed_messages = []
            for student in absent_students:
                try:
                    phone = get_first_phone_number(student.phone_numbers)
                    if not phone:
                        failed_messages.append({
                            'id': student.id,
                            'name': student.name,
                            'error': 'No valid phone numbers found'
                        })
                        continue
                    personalized_message = base_message.format(
                        student_name=student.name
                    )
                    result = sms_client.send_bulk_sms([phone], personalized_message)
                    student_detail = {
                        'id': student.id,
                        'name': student.name,
                        'class': student.class_name.name if student.class_name else None,
                        'section': student.section.name if student.section else None,
                        'phone': str(phone),
                        'message': personalized_message,
                        'sms_provider_response': result.get('data', {}),
                        'sms_status': result.get('status', 'unknown'),
                        'sms_message': result.get('message', '')
                    }
                    if result['status'] == 'success':
                        messages_sent.append(student_detail)
                    else:
                        failed_messages.append({
                            **student_detail,
                            'error': result['message']
                        })
                    student_details.append(student_detail)
                except Exception as e:
                    failed_messages.append({
                        'id': student.id,
                        'name': student.name,
                        'error': str(e)
                    })
            return self.get_success_response({
                'date': date,
                'total_recipients': len(student_details),
                'successful_messages': len(messages_sent),
                'failed_messages': len(failed_messages),
                'student_details': student_details,
                'failed_details': failed_messages
            }, f'Message sent successfully to {len(messages_sent)} out of {len(student_details)} absent students')
        except Exception as e:
            return self.get_error_response(f'Error sending messages: {str(e)}')




class AverageFeeCollectionAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            academic_year_name = request.query_params.get('academic_year')
            
            if not academic_year_name:
                today = now().date()
                
                current_academic_year = AcademicYear.objects.filter(
                    start_date__lte=today
                ).first()
                
                if not current_academic_year:
                    return Response({
                        'status': 'error',
                        'message': 'No active academic year found for current date'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                end_date = current_academic_year.start_date + relativedelta(months=current_academic_year.months_duration)
                
                if today > end_date:
                    return Response({
                        'status': 'error',
                        'message': 'No active academic year found for current date'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                academic_year_name = current_academic_year.name

            students_qs = Student.objects.filter(academic_year__name=academic_year_name,is_join=True,is_deleted=False)
            if not students_qs.exists():
                return Response({
                    'status': 'error',
                    'message': f'No students found for academic year: {academic_year_name}'
                }, status=status.HTTP_404_NOT_FOUND)

            academic_year_start = students_qs.first().academic_year.start_date
            today = now().date()

            days_elapsed = max((today - academic_year_start).days, 1)

            total_committed_fees = students_qs.aggregate(total=Sum('committed_fees'))['total'] or 0

            total_initial_paid = students_qs.aggregate(total=Sum('initial_fee_paid'))['total'] or 0

            total_fee_payments = Fees.objects.filter(student__academic_year__name=academic_year_name).aggregate(
                total=Sum('amount')
            )['total'] or 0

            total_fees_collected = total_initial_paid + total_fee_payments

            percentage_collected = 0
            if total_committed_fees > 0:
                percentage_collected = (total_fees_collected / total_committed_fees) * 100

            return Response({
                'status': 'success',
                'message': 'Average fee collection data retrieved successfully',
                'data': {
                    'academic_year': academic_year_name,
                    'academic_year_start_date': academic_year_start,
                    'today': today,
                    'days_elapsed': days_elapsed,
                    'total_committed_fees': float(total_committed_fees),
                    'total_fees_collected': float(total_fees_collected),
                    'percentage_of_committed_fees_collected': round(percentage_collected, 2),
                }
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error calculating average fee collection: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SectionByClassBatchView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request,section_id):
        # batch = request.query_params.get('batch')
        # group = request.query_params.get('group')

        # if not batch or not group:
            # return Response(
                # {"error": "batch, and group are required parameters"},
                # status=status.HTTP_400_BAD_REQUEST
            # )

        students = Student.objects.filter(
            section_id=section_id,
            # batch=batch,
            # group=group,
            is_join=True,
            is_deleted=False
        )

        serializer = StudentSectionSerializer(students, many=True)
        return Response(serializer.data)




class ClassAgainstSectionView(APIView):
    permission_classes = [IsAuthenticated]

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, class_id):
        try:
            class_obj = Class.objects.get(id=class_id)

            sections = Section.objects.filter(class_name=class_obj, is_active=True).order_by('name')

            section_list = []
            for section in sections:
                student_count = Student.objects.filter(section=section, is_join=True, is_deleted=False).count()

                section_list.append({
                    "id": section.id,
                    "name": section.name,
                    "current_strength": student_count,
                    "is_active": section.is_active
                })

            response_data = {
                "class": {
                    "id": class_obj.id,
                    "name": class_obj.name
                },
                "sections": section_list,
                "total_sections": len(section_list),
                "total_students": sum(section['current_strength'] for section in section_list),
            }

            return self.get_success_response(response_data, f"Sections for class {class_obj.name} fetched successfully.")

        except Class.DoesNotExist:
            return self.get_error_response(
                f"Class with ID {class_id} not found",
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return self.get_error_response(f"Error fetching sections: {str(e)}")

class StudentYearlyFeesListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StudentYearlyFeesSerializer

    def get_queryset(self):
        student_id = self.kwargs.get('student_id')
        return StudentYearlyFees.objects.filter(student_id=student_id,is_active=True).select_related('academic_year')

    def perform_create(self, serializer):
        student_id = self.kwargs.get('student_id')
        student = Student.objects.get(id=student_id)
        serializer.save(student=student, created_by=self.request.user)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Student yearly fees retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Student yearly fees created successfully")
        return self.get_error_response("Error creating student yearly fees", serializer.errors)

class StudentYearlyFeesDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StudentYearlyFeesSerializer

    def get_queryset(self):
        student_id = self.kwargs.get('student_id')
        return StudentYearlyFees.objects.filter(
            student_id=student_id,
            is_active=True
        ).select_related('academic_year')

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)

    def perform_destroy(self, instance):
        instance.is_active = False
        instance.modified_by = self.request.user
        instance.save()

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Student yearly fees retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Student yearly fees updated successfully")
        return self.get_error_response("Error updating student yearly fees", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Student yearly fees deleted successfully")
    
    
from rest_framework.generics import (
    ListCreateAPIView,
    RetrieveUpdateDestroyAPIView
)
from .models import StoreCategory, SubCategory, InventoryItem
from .serializers import (
    StoreCategorySerializer,
    SubCategorySerializer,
    InventoryItemSerializer
)

# Store Category Views
class StoreCategoryListCreateView(generics.ListCreateAPIView):
    queryset = StoreCategory.objects.all()
    serializer_class = StoreCategorySerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
class StoreCategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = StoreCategory.objects.all()
    serializer_class = StoreCategorySerializer
    lookup_field = 'pk'
    permission_classes = [IsAuthenticated]

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)

    def perform_destroy(self, instance):
        instance.is_active = False
        instance.modified_by = self.request.user
        instance.save()
        
# Sub Category Views
class SubCategoryListCreateView(generics.ListCreateAPIView):
    queryset = SubCategory.objects.all()
    serializer_class = SubCategorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        store_category_id = self.request.query_params.get('store_category_id')
        if store_category_id:
            queryset = queryset.filter(store_category_id=store_category_id)
        return queryset

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
class SubCategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = SubCategory.objects.all()
    serializer_class = SubCategorySerializer
    lookup_field = 'pk'
    permission_classes = [IsAuthenticated]

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)

    def put(self, request, *args, **kwargs):
        # Allow partial updates
        return self.update(request, *args, **kwargs, partial=True)

    def perform_destroy(self, instance):
        instance.is_active = False
        instance.modified_by = self.request.user
        instance.save()
        
# Inventory Item Views
class InventoryItemListCreateView(generics.ListCreateAPIView):
    queryset = InventoryItem.objects.filter(is_deleted=False)
    serializer_class = InventoryItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        store_category_id = self.request.query_params.get('store_category_id')
        subcategory_id = self.request.query_params.get('subcategory_id')
        
        if store_category_id:
            queryset = queryset.filter(store_category_id=store_category_id)
        if subcategory_id:
            queryset = queryset.filter(subcategory_id=subcategory_id)
        
        return queryset

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
class InventoryItemDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = InventoryItem.objects.all()
    serializer_class = InventoryItemSerializer
    lookup_field = 'pk'
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        # Force partial update for PUT requests
        return self.update(request, *args, **kwargs, partial=True)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)

    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()
        
        
class EoList(generics.ListCreateAPIView):
    # permission_classes = [GetPermission('Masters.View')]
    permission_classes = [IsAuthenticated]
    serializer_class = EducationalOfficerSerializer
    model = serializer_class.Meta.model
    queryset = model.objects.filter(is_deleted=False ).order_by('-id')
    filter_backends = [filters.SearchFilter, ]
    search_fields = ['name','code', ]
    ordering_fields = ['code',]


    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class EODetail(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = EducationalOfficerSerializer
    model = serializer_class.Meta.model
    queryset = model.objects.all()

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted=True
        instance.modified_by=self.request.user
        instance.save()




class MiscellaneousListCreateView(generics.ListCreateAPIView):
    queryset = Miscellaneous.objects.all()
    serializer_class = MiscellaneousSerializer
    permission_classes = [IsAuthenticated]
    # parser_classes = [MultiPartParser, FormParser]  # To handle file uploads

    def get_queryset(self):
        queryset = super().get_queryset()
        student_id = self.request.query_params.get('student_id')
        category = self.request.query_params.get('category')

        if student_id:
            queryset = queryset.filter(student_id=student_id)
        if category:
            queryset = queryset.filter(category=category)

        return queryset

    def create(self, request, *args, **kwargs):
        # Handle both single and bulk creation
        is_many = isinstance(request.data, list)
        serializer = self.get_serializer(data=request.data, many=is_many)
        serializer.is_valid(raise_exception=True)
        self.perform_bulk_create(serializer)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def perform_bulk_create(self, serializer):
        serializer.save(created_by=self.request.user)


class MiscellaneousDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Miscellaneous.objects.all()
    serializer_class = MiscellaneousSerializer
    permission_classes = [IsAuthenticated]

    def put(self, request, *args, **kwargs):
        # Allow partial updates
        return self.update(request, *args, **kwargs, partial=True)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)

    def perform_destroy(self, instance):
        instance.is_active = False
        instance.modified_by = self.request.user
        instance.save()

class BankNameListCreateView(generics.ListCreateAPIView):
    queryset = BankName.objects.all()
    serializer_class = BankNameSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

class BankNameDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = BankName.objects.all()
    serializer_class = BankNameSerializer
    permission_classes = [IsAuthenticated]

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted=True
        instance.modified_by=self.request.user
        instance.save()
        
class CasteListCreateView(generics.ListCreateAPIView):
    queryset = Caste.objects.all()
    serializer_class = CasteSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

class CasteDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Caste.objects.all()
    serializer_class = CasteSerializer
    permission_classes = [IsAuthenticated]

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted=True
        instance.modified_by=self.request.user
        instance.save()
        
        
class SubCasteListCreateView(generics.ListCreateAPIView):
    queryset = SubCaste.objects.all()
    serializer_class = SubCasteSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

class SubCasteDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = SubCaste.objects.all()
    serializer_class = SubCasteSerializer
    permission_classes = [IsAuthenticated]  

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted=True
        instance.modified_by=self.request.user
        instance.save()


class BulkTermPendingMessageView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        students = Student.objects.filter(is_deleted=False, is_join=True)
        sms_client = SpoorthiBulkSMSClient()
        successful = []
        failed = []
        for student in students:
            try:
                phone = get_first_phone_number(student.phone_numbers)
                if not phone:
                    raise ValueError("No valid phone numbers found")
                # Get all fee payments for the student
                from Masters.models import Fees
                fees_qs = Fees.objects.filter(student=student).order_by('-payment_date', '-created_on')
                latest_fee = fees_qs.first()
                latest_paid_amount = int(latest_fee.amount) if latest_fee else 0
                total_paid = int(student.initial_fee_paid) + int(fees_qs.aggregate(total=Sum('amount'))['total'] or 0)
                committed_fees = int(student.committed_fees)
                balance_amount = committed_fees - total_paid
                # Format message
                message = (
                    f"Dear Parent, Your Ward {student.name} Paid Rs.{latest_paid_amount} /-  Balance is Rs. {balance_amount} /-  Please clear the Fee balance amount.\u00A0Spoorthi Junior College"
                )
                # Send SMS
                result = sms_client.send_bulk_sms([phone], message)
                if result.get("status") == "success":
                    successful.append(str(student.id))
                else:
                    failed.append({
                        "id": str(student.id),
                        "name": student.name,
                        "error": result.get("message", "SMS sending failed")
                    })
            except Exception as e:
                failed.append({
                    "id": str(student.id),
                    "name": student.name,
                    "error": str(e)
                })
        return Response({
            "status": "success",
            "message": "Messages sent successfully",
            "data": {
                "total_students": students.count(),
                "messages_sent": len(successful),
                "failed_messages": len(failed),
                "details": {
                    "successful": successful,
                    "failed": failed
                }
            }
        }, status=status.HTTP_200_OK)

# class StudentAttendanceSummaryView(APIView):
#     permission_classes = [IsAuthenticated]

#     def get(self, request):
#         try:
#             # Get query parameters
#             date = request.query_params.get('date')
#             class_name = request.query_params.get('class')
#             section = request.query_params.get('section')
#             name = request.query_params.get('name')

#             # Base queryset for all active students
#             students = Student.objects.filter(is_join=True, is_deleted=False)

#             # Apply filters if provided
#             if class_name:
#                 students = students.filter(class_name__name__icontains=class_name)
#             if section:
#                 students = students.filter(section__name__icontains=section)
#             if name:
#                 students = students.filter(name__icontains=name)

#             # If date is provided, get attendance for that date
#             if date:
#                 try:
#                     date = datetime.strptime(date, '%Y-%m-%d').date()
#                 except ValueError:
#                     return Response({
#                         "status": "error",
#                         "message": "Invalid date format. Use YYYY-MM-DD"
#                     }, status=status.HTTP_400_BAD_REQUEST)

#                 # Get students with attendance records for the date
#                 students_with_attendance = students.filter(
#                     attendance__date=date
#                 ).select_related('class_name', 'section').distinct()

#                 # Get present and absent students
#                 present_students = students_with_attendance.filter(
#                     attendance__date=date,
#                     attendance__is_present=True
#                 ).distinct()

#                 absent_students = students_with_attendance.filter(
#                     attendance__date=date,
#                     attendance__is_present=False
#                 ).distinct()

#                 # Prepare response data
#                 response_data = {
#                     "date": date,
#                     "summary": {
#                         "total_students": students.count(),
#                         "total_with_attendance": students_with_attendance.count(),
#                         "present_count": present_students.count(),
#                         "absent_count": absent_students.count()
#                     },
#                     "present_students": [{
#                         "id": student.id,
#                         "name": student.name,
#                         "admission_no": student.admission_no,
#                         "class": student.class_name.name if student.class_name else None,
#                         "section": student.section.name if student.section else None,
#                         "group": student.group,
#                         "batch": student.batch
#                     } for student in present_students],
#                     "absent_students": [{
#                         "id": student.id,
#                         "name": student.name,
#                         "admission_no": student.admission_no,
#                         "class": student.class_name.name if student.class_name else None,
#                         "section": student.section.name if student.section else None,
#                         "group": student.group,
#                         "batch": student.batch
#                     } for student in absent_students]
#                 }

#             else:
#                 # If no date provided, return all students with their latest attendance
#                 response_data = {
#                     "summary": {
#                         "total_students": students.count()
#                     },
#                     "students": []
#                 }

#                 for student in students:
#                     latest_attendance = Attendance.objects.filter(
#                         student=student
#                     ).order_by('-date').first()

#                     student_data = {
#                         "id": student.id,
#                         "name": student.name,
#                         "admission_no": student.admission_no,
#                         "class": student.class_name.name if student.class_name else None,
#                         "section": student.section.name if student.section else None,
#                         "group": student.group,
#                         "batch": student.batch,
#                         "latest_attendance": {
#                             "date": latest_attendance.date if latest_attendance else None,
#                             "is_present": latest_attendance.is_present if latest_attendance else None
#                         }
#                     }
#                     response_data["students"].append(student_data)

#             return Response({
#                 "status": "success",
#                 "message": "Student attendance summary retrieved successfully",
#                 "data": response_data
#             })

#         except Exception as e:
#             return Response({
#                 "status": "error",
#                 "message": f"Error retrieving student attendance summary: {str(e)}"
#             }, status=status.HTTP_400_BAD_REQUEST)

class BulkStudentUploadView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = BulkStudentUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        file = serializer.validated_data['file']

        try:
            # ✅ Step 1: Read Excel (header at second row)
            df = pd.read_excel(file, header=1)

            # ✅ Step 2: Normalize column headers
            df.columns = [col.strip().replace('\xa0', ' ').replace('\n', ' ').lower() for col in df.columns]

            # ✅ Step 3: Auto-correct common typos in headers
            rename_map = {
                'name of the father  / mother': 'name of the father / mother',
                'aadhhar no': 'aadhaar no',
            }
            df.rename(columns=rename_map, inplace=True)

            # ✅ Step 4: Required fields
            required_columns = [
                'group', 'course', 'student id', 'name of the student',
                'name of the father / mother', 'aadhaar no', 'dob', 'contact no'
            ]
            missing_cols = [col for col in required_columns if col not in df.columns]
            if missing_cols:
                return Response({
                    "error": f"Excel file must contain columns: {', '.join(required_columns)}. "
                             f"Missing: {', '.join(missing_cols)}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # ✅ Step 5: Prepare group/batch lookup for case-insensitive match
            group_choices = {k.lower(): k for k, _ in Student.GROUP_CHOICES}
            batch_choices = {k.lower(): k for k, _ in Student.BATCH_CHOICES}

            success_count = 0
            errors = []
            created_students = []

            with transaction.atomic():
                for index, row in df.iterrows():
                    row_num = index + 2

                    try:
                        raw_batch = str(row['group']).strip()
                        raw_group = str(row['course']).strip()
                        admission_no = str(row['student id']).strip()
                        name = str(row['name of the student']).strip()
                        father_name = str(row['name of the father / mother']).strip()
                        student_aadhar = str(row['aadhaar no']).strip() if not pd.isna(row['aadhaar no']) else ''
                        dob = pd.to_datetime(row['dob']).date() if not pd.isna(row['dob']) else None
                        phone_number = str(row['contact no']).strip() if not pd.isna(row['contact no']) else ''



                        if not all([raw_batch, raw_group, admission_no, name, father_name, dob]):
                            errors.append({'row': row_num, 'error': "Missing one or more required fields"})
                            continue


                        # ✅ Match case-insensitive, map to original casing
                        group_key = raw_group.lower()
                        batch_key = raw_batch.lower()

                        if group_key not in group_choices:
                            errors.append({'row': row_num, 'error': f"Invalid group: {raw_group}"})
                            continue
                        if batch_key not in batch_choices:
                            errors.append({'row': row_num, 'error': f"Invalid batch: {raw_batch}"})
                            continue

                        group = group_choices[group_key]
                        batch = batch_choices[batch_key]

                        student_data = {
                            'group': group,
                            'batch': batch,
                            'admission_no': admission_no,
                            'name': name,
                            'father_name': father_name,
                            'student_aadhar': student_aadhar,
                            'dob': dob,
                            'phone_numbers': [phone_number] if phone_number else [],
                            'status': 'admission',
                            'committed_fees': 0,
                            'initial_fee_paid': 0,
                        }
                        
    
                        student_serializer = InitialStudentSerializer(data=student_data)
                        if student_serializer.is_valid():
                            student = student_serializer.save()
                            created_students.append(student_serializer.data)
                            success_count += 1
                        else:
                            errors.append({'row': row_num, 'error': student_serializer.errors})

                    except Exception as e:
                        errors.append({'row': row_num, 'error': str(e)})

            return Response({
                "status": "success",
                "message": f"Successfully processed {success_count} students.",
                "created": created_students,
                "errors": errors if errors else None
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Error processing file: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)

class IndividualAttendanceMessageView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        student_id = request.data.get('student_id')
        date = request.data.get('date')
        sms_client = SpoorthiBulkSMSClient()
        successful = []
        failed = []
        if not student_id or not date:
            return Response({
                "status": "error",
                "message": "student_id and date are required"
            }, status=400)
        try:
            student = Student.objects.get(id=uuid.UUID(student_id))
        except Student.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Student not found"
            }, status=404)
        # Check attendance
        was_absent = Attendance.objects.filter(student=student, date=date, is_present=False).exists()
        if not was_absent:
            failed.append({
                "id": str(student.id),
                "name": student.name,
                "error": "Student was not absent on this date"
            })
        else:
            phone = get_first_phone_number(student.phone_numbers)
            if not phone:
                failed.append({
                    "id": str(student.id),
                    "name": student.name,
                    "error": "No valid phone numbers found"
                })
            else:
                message = f"Dear Parent, Your Ward {student.name} was absent today. Regular attendance is key to progress. Spoorthi Junior College"
                result = sms_client.send_bulk_sms([phone], message)
                if result.get("status") == "success":
                    successful.append(str(student.id))
                else:
                    failed.append({
                        "id": str(student.id),
                        "name": student.name,
                        "error": result.get("message", "SMS sending failed")
                    })
        return Response({
            "status": "success",
            "message": "Attendance message processed",
            "data": {
                "messages_sent": len(successful),
                "failed_messages": len(failed),
                "details": {
                    "successful": successful,
                    "failed": failed
                }
            }
        }, status=status.HTTP_200_OK)

# Utility function to extract the first phone number from various formats
def get_first_phone_number(pn):
    if isinstance(pn, list):
        if not pn:
            return None
        # If the first element is a string with commas, split and take the first
        first = pn[0]
        if isinstance(first, str) and ',' in first:
            return first.split(',')[0].strip()
        return str(first).strip()
    elif isinstance(pn, str):
        # If the string contains commas, split and take the first
        return pn.split(',')[0].strip()
    elif isinstance(pn, int):
        return str(pn)
    return None

class IndividualMarksMessageView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        student_id = request.data.get('student_id')
        test_name = request.data.get('test_name')
        sms_client = SpoorthiBulkSMSClient()
        successful = []
        failed = []
        
        if not student_id or not test_name:
            return Response({
                "status": "error",
                "message": "student_id and test_name are required"
            }, status=400)
        
        try:
            student = Student.objects.get(id=uuid.UUID(student_id))
        except Student.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Student not found"
            }, status=404)
        
        # Get student's test result for the specified test
        test_result = StudentTestResult.objects.filter(
            student=student,
            test_name=test_name
        ).prefetch_related('subject_marks').first()
        
        if not test_result or not test_result.subject_marks.exists():
            failed.append({
                "id": str(student.id),
                "name": student.name,
                "error": f"No marks found for test: {test_name}"
            })
        else:
            # Calculate total marks
            total_marks_obtained = sum(float(mark.marks_obtained) for mark in test_result.subject_marks.all())
            total_max_marks = sum(float(mark.total_marks) for mark in test_result.subject_marks.all())
            total_rank = test_result.rank if test_result.rank is not None else ''
            # Build subject string as CODE:marks/total(rank)
            subject_strs = []
            for mark in test_result.subject_marks.all():
                code = mark.subject[:3].upper()
                marks = int(mark.marks_obtained)
                total = int(mark.total_marks)
                rank = f"({mark.rank})" if mark.rank is not None else ""
                subject_strs.append(f"{code}:{marks}/{total}{rank}")
            subjects_part = ','.join(subject_strs)
            # New message template
            message = (
                f"Dear Parent, Your ward {student.name} , Marks of Exam: {test_name}, Subjects: {subjects_part}, Total:{int(total_marks_obtained)}/{int(total_max_marks)}{f'({total_rank})' if total_rank else ''}.\u00A0Spoorthi Junior College"
            )
            # Send SMS
            phone = get_first_phone_number(student.phone_numbers)
            if not phone:
                failed.append({
                    "id": str(student.id),
                    "name": student.name,
                    "error": "No valid phone numbers found"
                })
            else:
                result = sms_client.send_bulk_sms([phone], message)
                if result.get("status") == "success":
                    successful.append(str(student.id))
                else:
                    failed.append({
                        "id": str(student.id),
                        "name": student.name,
                        "error": result.get("message", "SMS sending failed")
                    })
        
        return Response({
            "status": "success",
            "message": "Marks message processed",
            "data": {
                "messages_sent": len(successful),
                "failed_messages": len(failed),
                "details": {
                    "successful": successful,
                    "failed": failed
                }
            }
        }, status=status.HTTP_200_OK)

# Utility function to get short name as 'F Lastname' from full name
def get_short_name(full_name):
    if not full_name:
        return ''
    parts = str(full_name).strip().split()
    if len(parts) == 1:
        return parts[0]
    return f"{parts[0][0]} {parts[-1]}"

class BulkMarksMessageView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        test_name = request.data.get('test_name')
        sms_client = SpoorthiBulkSMSClient()
        successful = []
        failed = []
        
        if not test_name:
            return Response({
                "status": "error",
                "message": "test_name is required"
            }, status=400)
        
        # Get all test results for this test
        test_results = StudentTestResult.objects.filter(
            test_name=test_name,
            student__is_deleted=False,
            student__is_join=True
        ).select_related('student').prefetch_related('subject_marks')
        
        if not test_results.exists():
            return Response({
                "status": "error",
                "message": f"No students found with marks for test: {test_name}"
            }, status=404)
        
        for test_result in test_results:
            student = test_result.student
            try:
                if not test_result.subject_marks.exists():
                    failed.append({
                        "id": str(student.id),
                        "name": student.name,
                        "error": f"No marks found for test: {test_name}"
                    })
                    continue
                # Calculate total marks
                total_marks_obtained = sum(float(mark.marks_obtained) for mark in test_result.subject_marks.all())
                total_max_marks = sum(float(mark.total_marks) for mark in test_result.subject_marks.all())
                total_rank = test_result.rank if test_result.rank is not None else ''
                # Build subject string as CODE:marks/total(rank)
                subject_strs = []
                for mark in test_result.subject_marks.all():
                    code = mark.subject[:3].upper()
                    marks = int(mark.marks_obtained)
                    total = int(mark.total_marks)
                    rank = f"({mark.rank})" if mark.rank is not None else ""
                    subject_strs.append(f"{code}:{marks}/{total}{rank}")
                subjects_part = ','.join(subject_strs)
                # Use short name for student
                short_name = get_short_name(student.name)
                # New message template
                message = (
                    f"Dear Parent, Your ward {short_name} , Marks of Exam: {test_name}, Subjects: {subjects_part}, Total:{int(total_marks_obtained)}/{int(total_max_marks)}{f'({total_rank})' if total_rank else ''}.\u00A0Spoorthi Junior College"
                )
                phone = get_first_phone_number(student.phone_numbers)
                if not phone:
                    failed.append({
                        "id": str(student.id),
                        "name": student.name,
                        "error": "No valid phone numbers found"
                    })
                else:
                    result = sms_client.send_bulk_sms([phone], message)
                    if result.get("status") == "success":
                        successful.append(str(student.id))
                    else:
                        failed.append({
                            "id": str(student.id),
                            "name": student.name,
                            "error": result.get("message", "SMS sending failed")
                        })
            except Exception as e:
                failed.append({
                    "id": str(student.id),
                    "name": student.name,
                    "error": str(e)
                })
        return Response({
            "status": "success",
            "message": "Bulk marks messages processed",
            "data": {
                "total_students": test_results.count(),
                "messages_sent": len(successful),
                "failed_messages": len(failed),
                "details": {
                    "successful": successful,
                    "failed": failed
                }
            }
        }, status=status.HTTP_200_OK)
