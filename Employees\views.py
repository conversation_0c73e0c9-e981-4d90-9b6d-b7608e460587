from django.shortcuts import render
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db import transaction
from django.utils import timezone
from django.db.models import Q, Sum, Count
from datetime import datetime, timedelta, date, time
import calendar
import pandas as pd
import PyPDF2
import io
import re
from dateutil import parser

from Masters.models import Fees, Student
from .models import Department, Category, Employee, EmployeeAttendance,Leave, Holiday, SalaryRecord, Expense

from .serializers import DepartmentSerializer, CategorySerializer, EmployeeSerializer,EmployeeAttendanceSerializer, BulkEmployeeAttendanceUploadSerializer,LeaveSerializer, HolidaySerializer, SalaryCalculationSerializer,SalaryCalculationResponseSerializer, BulkHolidayUploadSerializer,SalaryRecordSerializer, ExpenseSerializer, ExpenseSummarySerializer
from utils.msg91 import SpoorthiBulkSMSClient
from django.core.exceptions import ObjectDoesNotExist
from openpyxl import load_workbook
from rest_framework.parsers import FileUploadParser
from django.utils.dateparse import parse_date

from calendar import month_name
from django.utils.timezone import now


from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework import filters
from django_filters.rest_framework import DjangoFilterBackend, FilterSet
from django_filters import DateRangeFilter,DateFilter

class EmployeeFilter(FilterSet):
    date_range = DateRangeFilter(field_name='date')
    start_date = DateFilter(field_name='date',lookup_expr=('gte'),)
    end_date = DateFilter(field_name='date',lookup_expr=('lte'))

    class Meta:
        model = Employee
        fields = ['employee_no', 'name','start_date', 'end_date']


class EmployeeListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Employee.objects.all()
    serializer_class = EmployeeSerializer
    filterset_class = EmployeeFilter
    filter_backends = [filters.SearchFilter, DjangoFilterBackend,]
    search_fields = ['employee_no', 'name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    # def get(self, request, *args, **kwargs):
    #     queryset = self.get_queryset()
    #     serializer = self.get_serializer(queryset, many=True)
    #     return self.get_success_response(serializer.data, "Employees retrieved successfully")
    
    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        today = date.today()
        first_day = today.replace(day=1)

        holiday_dates = set(
            Holiday.objects.filter(date__range=(first_day, today)).values_list('date', flat=True)
        )

        response_data = []
        for employee in queryset:
            serializer = self.get_serializer(employee)

            attendance_records = EmployeeAttendance.objects.filter(
                employee=employee,
                date__range=(first_day, today)
            )

            present_days = attendance_records.filter(is_present=True).count()

            sick_leave_days = attendance_records.filter(
                is_present=False,
                remarks__iexact='SL'  # Case-insensitive match
            ).count()

            absent_days = attendance_records.filter(
                is_present=False
            ).exclude(remarks__iexact='SL').count()

            employee_data = serializer.data
            employee_data.update({
                "present_days": present_days,
                "absent_days": absent_days,
                "sick_leave_count": sick_leave_days,
                "holiday_count": len(holiday_dates),
            })
            response_data.append(employee_data)

        return self.get_success_response(response_data, "Employees with current month attendance stats")


    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Employee created successfully")
        return self.get_error_response("Error creating employee", serializer.errors)


class EmployeeDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Employee.objects.all()
    serializer_class = EmployeeSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Employee retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Employee updated successfully")
        return self.get_error_response("Error updating employee", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Employee deleted successfully")


class HolidayListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Holiday.objects.all()
    serializer_class = HolidaySerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Holidays retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Holiday created successfully")
        return self.get_error_response("Error creating holiday", serializer.errors)


class HolidayDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Holiday.objects.all()
    serializer_class = HolidaySerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Holiday retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Holiday updated successfully")
        return self.get_error_response("Error updating holiday", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Holiday deleted successfully")

class EmpAttendanceFilter(FilterSet):
    # date_range = DateRangeFilter(field_name='date')
    start_date = DateFilter(field_name='date',lookup_expr=('gte'),)
    end_date = DateFilter(field_name='date',lookup_expr=('lte'))

    class Meta:
        model = EmployeeAttendance
        fields = ['employee', 'start_date', 'end_date']


class EmployeeAttendanceListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = EmployeeAttendance.objects.all()
    serializer_class = EmployeeAttendanceSerializer
    filterset_class = EmpAttendanceFilter
    filter_backends = [DjangoFilterBackend]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    # def perform_update(self, serializer):
    #     serializer.save(modified_by=self.request.user)
        
    # def perform_destroy(self, instance):
    #     instance.is_deleted = True
    #     instance.modified_by = self.request.user
    #     instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Employee attendance records retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Employee attendance record created successfully")
        return self.get_error_response("Error creating employee attendance record", serializer.errors)


class EmployeeAttendanceDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = EmployeeAttendance.objects.all()
    serializer_class = EmployeeAttendanceSerializer

    # def perform_create(self, serializer):
    #     serializer.save(created_by=self.request.user, modified_by=self.request.user)

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()

    def get_queryset(self):
        return super().get_queryset().filter(is_deleted=False)

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Employee attendance record retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Employee attendance record updated successfully")
        return self.get_error_response("Error updating employee attendance record", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Employee attendance record deleted successfully")

# Department Views
class DepartmentListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer


    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Departments retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Department created successfully")
        return self.get_error_response("Error creating department", serializer.errors)

class DepartmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()
    
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Department retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Department updated successfully")
        return self.get_error_response("Error updating department", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Department deleted successfully")

# Category Views
class CategoryListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Category.objects.all()
    serializer_class = CategorySerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Categories retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Category created successfully")
        return self.get_error_response("Error creating category", serializer.errors)

class CategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Category.objects.all()
    serializer_class = CategorySerializer

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()
    
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Category retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Category updated successfully")
        return self.get_error_response("Error updating category", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Category deleted successfully")

# Employee Attendance Views
class EmployeeAttendanceView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, employee_id):
        try:
            # Get month and year from query parameters, default to current month/year
            month = int(request.query_params.get('month', timezone.now().month))
            year = int(request.query_params.get('year', timezone.now().year))

            # Validate month
            if not (1 <= month <= 12):
                return Response({
                    "status": "error",
                    "message": "Month must be between 1 and 12"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get employee
            employee = Employee.objects.get(id=employee_id)
            basic_salary = employee.salary

            # Get first and last day of the month
            first_day = datetime(year, month, 1).date()
            last_day = datetime(year, month, calendar.monthrange(year, month)[1]).date()

            # Get holidays
            holidays = Holiday.objects.filter(date__gte=first_day, date__lte=last_day)
            holiday_dates = set(holiday.date for holiday in holidays)

            # Get attendance records
            attendance_records = EmployeeAttendance.objects.filter(
                employee=employee,
                date__gte=first_day,
                date__lte=last_day
            ).order_by('-date')

            # Calculate total working days (including holidays)
            total_working_days = (last_day - first_day).days + 1

            # Calculate present, absent, and sick leave days
            present_days = attendance_records.filter(is_present=True, remarks__isnull=True).count()
            absent_days = attendance_records.filter(is_present=False, remarks__isnull=True).count()
            sick_leave_days = attendance_records.filter(remarks='SL').count()

            # Get present, absent, and sick leave dates
            present_dates = [record.date for record in attendance_records if record.is_present and not record.remarks]
            absent_dates = [record.date for record in attendance_records if not record.is_present and not record.remarks]
            sick_leave_dates = [record.date for record in attendance_records if record.remarks == 'SL']

            # Calculate per day salary
            per_day_salary = basic_salary / total_working_days if total_working_days > 0 else 0

            # Calculate salary components
            working_days_salary = per_day_salary * present_days
            holiday_salary = per_day_salary * len(holiday_dates)
            
            # Calculate sick leave salary (first day is paid)
            paid_sick_leave_days = min(sick_leave_days, 1)
            sick_leave_salary = per_day_salary * paid_sick_leave_days

            # Calculate total salary
            total_salary = working_days_salary + holiday_salary + sick_leave_salary

            # Update employee with salary information
            employee.current_month = month
            employee.current_year = year
            employee.total_working_days = total_working_days
            employee.present_days = present_days
            employee.absent_days = absent_days
            employee.holiday_days = len(holiday_dates)
            employee.per_day_salary = round(per_day_salary, 2)
            employee.working_days_salary = round(working_days_salary, 2)
            employee.holiday_salary = round(holiday_salary, 2)
            employee.total_salary = round(total_salary, 2)
            employee.save()

            # Prepare response
            response_data = {
                "status": "success",
                "message": "Employee attendance records retrieved successfully",
                "employee_name": employee.name,
                "month": month,
                "year": year,
                "statistics": {
                    "total_working_days": total_working_days,
                    "present_days": present_days,
                    "absent_days": absent_days,
                    "holiday_days": len(holiday_dates),
                    "sick_leave_days": sick_leave_days
                },
                "dates": {
                    "present_dates": [date.strftime('%Y-%m-%d') for date in sorted(present_dates)],
                    "absent_dates": [date.strftime('%Y-%m-%d') for date in sorted(absent_dates)],
                    "sick_leave_dates": [date.strftime('%Y-%m-%d') for date in sorted(sick_leave_dates)],
                    "holiday_dates": [date.strftime('%Y-%m-%d') for date in sorted(list(holiday_dates))]
                },
                "salary": {
                    "total_salary": round(total_salary, 2)
                }
            }

            return Response(response_data)

        except Employee.DoesNotExist:
            return Response({
                "status": "error",
                "message": f"No employee found with ID {employee_id}"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Error retrieving attendance records: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)

class BulkEmployeeAttendanceUploadView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = BulkEmployeeAttendanceUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        file = serializer.validated_data['file']
        default_date = serializer.validated_data.get('date', timezone.now().date())

        try:
            df = pd.read_excel(file)
            required_columns = ['employee_no', 'date', 'attendance_status']
            if not all(col in df.columns for col in required_columns):
                return Response({
                    "error": f"Excel file must contain columns: {', '.join(required_columns)}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Optional fields
            has_check_in = 'check_in_time' in df.columns
            has_check_out = 'check_out_time' in df.columns

            errors = []
            success_count = 0
            attendance_records = []

            with transaction.atomic():
                for index, row in df.iterrows():
                    try:
                        employee_no = str(row['employee_no'])
                        date = pd.to_datetime(row['date']).date() if pd.notna(row['date']) else default_date
                        attendance_status = str(row['attendance_status']).strip()

                        if date > timezone.now().date():
                            errors.append({
                                'row': index + 2,
                                'error': f"Date {date} cannot be in the future"
                            })
                            continue

                        if attendance_status == 'Present':
                            is_present = True
                            remarks = None
                        elif attendance_status == 'Absent':
                            is_present = False
                            remarks = None
                        elif attendance_status == 'SL':
                            is_present = False
                            remarks = 'SL'
                        else:
                            errors.append({
                                'row': index + 2,
                                'error': f"Invalid attendance status: {attendance_status}. Must be 'Present', 'Absent', or 'SL'"
                            })
                            continue

                        # Optional time fields
                        check_in_time = pd.to_datetime(row['check_in_time']).time() if has_check_in and pd.notna(row['check_in_time']) else None
                        check_out_time = pd.to_datetime(row['check_out_time']).time() if has_check_out and pd.notna(row['check_out_time']) else None

                        employee = Employee.objects.get(employee_no=employee_no)
                        attendance_records.append(EmployeeAttendance(
                            employee=employee,
                            date=date,
                            is_present=is_present,
                            remarks=remarks,
                            check_in_time=check_in_time,
                            check_out_time=check_out_time,
                            created_by=request.user
                        ))
                        success_count += 1

                    except Employee.DoesNotExist:
                        errors.append({
                            'row': index + 2,
                            'error': f"Employee with ID {employee_no} not found"
                        })
                    except ValueError as e:
                        errors.append({
                            'row': index + 2,
                            'error': f"Invalid data in row: {str(e)}"
                        })
                    except Exception as e:
                        errors.append({
                            'row': index + 2,
                            'error': f"Error processing row: {str(e)}"
                        })

                if attendance_records:
                    for attendance in attendance_records:
                        attendance.save()

            return Response({
                "status": "success",
                "message": f"Successfully processed {success_count} attendance records",
                "errors": errors if errors else None
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Error processing file: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)

# Leave Views
class LeaveListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Leave.objects.select_related('employee').all()
    serializer_class = LeaveSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get_queryset(self):
        queryset = super().get_queryset()
        employee_id = self.request.query_params.get('employee_id')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        leave_type = self.request.query_params.get('leave_type')
        is_approved = self.request.query_params.get('is_approved')

        if employee_id:
            queryset = queryset.filter(employee_id=employee_id)
        if start_date:
            queryset = queryset.filter(start_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(end_date__lte=end_date)
        if leave_type:
            queryset = queryset.filter(leave_type=leave_type)
        if is_approved is not None:
            queryset = queryset.filter(is_approved=is_approved.lower() == 'true')

        return queryset

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Leaves retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Leave created successfully")
        return self.get_error_response("Error creating leave", serializer.errors)

class LeaveDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Leave.objects.select_related('employee').all()
    serializer_class = LeaveSerializer

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()
        
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Leave retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Leave updated successfully")
        return self.get_error_response("Error updating leave", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Leave deleted successfully")

# Salary Calculation View
class SalaryCalculationView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, employee_id, year, month):
        try:
            # Validate month
            if not (1 <= month <= 12):
                return Response({
                    "status": "error",
                    "message": "Month must be between 1 and 12"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get employee
            employee = Employee.objects.get(id=employee_id)
            basic_salary = employee.salary

            # Get first and last day of the month
            first_day = datetime(year, month, 1).date()
            last_day = datetime(year, month, calendar.monthrange(year, month)[1]).date()

            # Get holidays
            holidays = Holiday.objects.filter(date__gte=first_day, date__lte=last_day)
            holiday_dates = set(holiday.date for holiday in holidays)

            # Get attendance records
            attendance_records = EmployeeAttendance.objects.filter(
                employee=employee,
                date__gte=first_day,
                date__lte=last_day
            )

            # Calculate total working days (excluding holidays)
            total_working_days = (last_day - first_day).days + 1 - len(holiday_dates)

            # Calculate present, absent, and sick leave days from attendance records
            present_days = attendance_records.filter(is_present=True).count()
            absent_days = attendance_records.filter(is_present=False, remarks__isnull=True).count()
            sick_leave_days = attendance_records.filter(remarks='SL').count()

            # Calculate per day salary
            per_day_salary = basic_salary / total_working_days if total_working_days > 0 else 0

            # Calculate salary components
            working_days_salary = per_day_salary * present_days
            holiday_salary = per_day_salary * len(holiday_dates)
            
            # Calculate sick leave salary (first day is paid)
            paid_sick_leave_days = min(sick_leave_days, 1)
            sick_leave_salary = per_day_salary * paid_sick_leave_days

            # Calculate total salary
            total_salary = working_days_salary + holiday_salary + sick_leave_salary

            # Calculate deductions
            absent_deduction = per_day_salary * absent_days
            
            # Calculate sick leave deduction (only for days beyond first day)
            unpaid_sick_leave = max(0, sick_leave_days - 1)
            sick_leave_deduction = per_day_salary * unpaid_sick_leave

            total_deductions = absent_deduction + sick_leave_deduction

            # Calculate net salary
            net_salary = total_salary - total_deductions

            # Create or update salary record
            salary_record, created = SalaryRecord.objects.update_or_create(
                employee=employee,
                month=month,
                year=year,
                defaults={
                    'basic_salary': basic_salary,
                    'total_working_days': total_working_days,
                    'present_days': present_days,
                    'absent_days': absent_days,
                    'holiday_days': len(holiday_dates),
                    'per_day_salary': round(per_day_salary, 2),
                    'working_days_salary': round(working_days_salary, 2),
                    'holiday_salary': round(holiday_salary, 2),
                    'total_salary': round(total_salary, 2),
                    'net_salary': round(net_salary, 2)
                }
            )

            # Prepare response
            response_data = {
                'employee_name': employee.name,
                'basic_salary': basic_salary,
                'month': month,
                'year': year,
                'total_working_days': total_working_days,
                'present_days': present_days,
                'absent_days': absent_days,
                'holiday_days': len(holiday_dates),
                'sick_leave_days': sick_leave_days,
                'per_day_salary': round(per_day_salary, 2),
                'salary_components': {
                    'working_days_salary': round(working_days_salary, 2),
                    'holiday_salary': round(holiday_salary, 2),
                    'sick_leave_salary': round(sick_leave_salary, 2),
                    'total_salary': round(total_salary, 2)
                },
                'deductions': {
                    'absent_deduction': round(absent_deduction, 2),
                    'sick_leave_deduction': round(sick_leave_deduction, 2),
                    'total_deductions': round(total_deductions, 2)
                },
                'net_salary': round(net_salary, 2)
            }

            response_serializer = SalaryCalculationResponseSerializer(response_data)
            return Response({
                "status": "success",
                "message": "Salary calculated and saved successfully",
                "data": response_serializer.data
            })

        except Employee.DoesNotExist:
            return Response({
                "status": "error",
                "message": f"No employee found with ID {employee_id}"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Error calculating salary: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)

class BulkHolidayUploadView(APIView):
    permission_classes = [IsAuthenticated]

    def extract_holidays_from_pdf(self, pdf_file, year):
        holidays = []
        try:
            # Read PDF file
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            # Extract text from all pages
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text()

            # Regular expression pattern for dates (adjust based on your PDF format)
            # This pattern looks for dates in various formats
            date_patterns = [
                r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}',  # DD-MM-YYYY or DD/MM/YYYY
                r'\d{1,2}\s+[A-Za-z]+\s+\d{2,4}',  # DD Month YYYY
                r'[A-Za-z]+\s+\d{1,2}(?:st|nd|rd|th)?\s+\d{2,4}'  # Month DD YYYY
            ]

            # Split text into lines and process each line
            lines = text.split('\n')
            for line in lines:
                # Try to find a date in the line
                date_found = False
                for pattern in date_patterns:
                    date_match = re.search(pattern, line)
                    if date_match:
                        try:
                            # Parse the date
                            date_str = date_match.group()
                            date = parser.parse(date_str)
                            
                            # Check if the date is in the specified year
                            if date.year == year:
                                # Extract holiday name (assuming it's before the date)
                                name = line[:date_match.start()].strip()
                                if not name:
                                    # If no name before date, try after
                                    name = line[date_match.end():].strip()
                                
                                if name:
                                    holidays.append({
                                        'name': name,
                                        'date': date.date(),
                                        'description': None
                                    })
                                date_found = True
                                break
                        except:
                            continue

        except Exception as e:
            raise Exception(f"Error processing PDF: {str(e)}")

        return holidays

    def process_excel_file(self, file, year):
        df = pd.read_excel(file)
        
        required_columns = ['name', 'date', 'description']
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"Excel file must contain columns: {', '.join(required_columns)}")

        holidays = []
        for index, row in df.iterrows():
            try:
                name = str(row['name']).strip()
                date = pd.to_datetime(row['date']).date()
                description = str(row['description']).strip() if pd.notna(row['description']) else None

                if date.year != year:
                    continue

                holidays.append({
                    'name': name,
                    'date': date,
                    'description': description
                })
            except:
                continue

        return holidays

    def post(self, request):
        serializer = BulkHolidayUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                "status": "error",
                "message": "Invalid input data",
                "errors": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            file = serializer.validated_data['file']
            year = serializer.validated_data.get('year', timezone.now().year)
            file_type = serializer.validated_data['file_type']

            # Process file based on type
            if file_type == 'excel':
                holidays = self.process_excel_file(file, year)
            else:  # PDF
                holidays = self.extract_holidays_from_pdf(file, year)

            errors = []
            success_count = 0

            with transaction.atomic():
                for holiday_data in holidays:
                    try:
                        # Check if holiday already exists for this date
                        if Holiday.objects.filter(date=holiday_data['date']).exists():
                            errors.append({
                                'date': holiday_data['date'],
                                'error': f"Holiday already exists for date {holiday_data['date']}"
                            })
                            continue

                        # Create holiday one by one to ensure code generation
                        holiday = Holiday(
                            name=holiday_data['name'],
                            date=holiday_data['date'],
                            description=holiday_data['description'],
                            created_by=request.user
                        )
                        holiday.save()  # This will trigger the save method and generate the code
                        success_count += 1

                    except Exception as e:
                        errors.append({
                            'date': holiday_data['date'],
                            'error': f"Error processing holiday: {str(e)}"
                        })

            return Response({
                "status": "success",
                "message": f"Successfully processed {success_count} holiday records",
                "errors": errors if errors else None
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Error processing file: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)

class SalaryRecordListView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SalaryRecordSerializer
    queryset = SalaryRecord.objects.select_related('employee', 'employee__department', 'employee__category').all()

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
    def get_queryset(self):
        queryset = super().get_queryset()
        employee_name = self.request.query_params.get('employee_name')
        month = self.request.query_params.get('month')
        year = self.request.query_params.get('year')
        payment_date = self.request.query_params.get('payment_date')  # exact date

        if employee_name:
            queryset = queryset.filter(employee__name__icontains=employee_name)
        if month:
            queryset = queryset.filter(month=month)
        if year:
            queryset = queryset.filter(year=year)
        if payment_date:
            queryset = queryset.filter(payment_date=parse_date(payment_date))
        
        return queryset

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        if not queryset.exists():
            return self.get_success_response([], "No salary records found")

        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Salary records retrieved successfully")

class SalaryRecordDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SalaryRecordSerializer
    queryset = SalaryRecord.objects.select_related('employee', 'employee__department', 'employee__category').all()

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        if not partial:
            current_data = self.get_serializer(instance).data
            request_data = {**current_data, **request.data}
            
            # Preserve read-only fields from being changed
            for field in self.serializer_class.Meta.read_only_fields:
                if field in current_data:
                    request_data[field] = current_data[field]

            if 'employee_id' not in request_data:
                request_data['employee_id'] = str(instance.employee.id) if instance.employee else None
        else:
            request_data = request.data
            if 'employee_id' not in request_data and instance.employee:
                request_data['employee_id'] = str(instance.employee.id)

        serializer = self.get_serializer(instance, data=request_data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return self.get_success_response(serializer.data, "Salary record updated successfully")


    def put(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)

    def patch(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)
    
    
class EmployeeSalaryHistoryView(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SalaryRecordSerializer

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        try:
            # Get employee
            employee = Employee.objects.get(id=kwargs.get('employee_id'))
            
            # Get salary records for the employee
            salary_records = SalaryRecord.objects.filter(employee=employee).order_by('-year', '-month')
            
            if not salary_records.exists():
                return self.get_success_response([], f"No salary records found for {employee.name}")
            
            # Prepare response data
            response_data = {
                "employee_name": employee.name,
                "salary_history": []
            }
            
            for record in salary_records:
                response_data["salary_history"].append({
                    "month_year": f"{record.month}-{record.year}",
                    "total_salary": round(record.total_salary, 2)
                })
            
            return self.get_success_response(response_data, f"Salary history retrieved successfully for {employee.name}")
            
        except Employee.DoesNotExist:
            return self.get_error_response(
                f"No employee found with ID {kwargs.get('employee_id')}",
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return self.get_error_response(f"Error retrieving salary history: {str(e)}")

class ExpenseListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Expense.objects.all()
    serializer_class = ExpenseSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
        
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get_queryset(self):
        queryset = super().get_queryset()
        month = self.request.query_params.get('month')
        year = self.request.query_params.get('year')
        name = self.request.query_params.get('name')
        # queryset = super().get_queryset()
        # employee_name = self.request.query_params.get('employee_name')
        # month = self.request.query_params.get('month')
        # year = self.request.query_params.get('year')
        date = self.request.query_params.get('date')  # exact date
        
        if month and year:
            queryset = queryset.filter(date__year=year,date__month=month)
        if date:
            queryset = queryset.filter(date=parse_date(date))
        if name:
            queryset = queryset.filter(name__icontains=name)
    
        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return self.get_success_response(serializer.data, "Expenses retrieved successfully")

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return self.get_success_response(serializer.data, "Expense created successfully")
        return self.get_error_response("Error creating expense", serializer.errors)

class ExpenseDetailView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated]
    queryset = Expense.objects.all()
    serializer_class = ExpenseSerializer

    def perform_update(self, serializer):
        serializer.save(modified_by=self.request.user)
        
    def perform_destroy(self, instance):
        instance.is_deleted = True
        instance.modified_by = self.request.user
        instance.save()
        
    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
            "errors": errors
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return self.get_success_response(serializer.data, "Expense retrieved successfully")

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        if serializer.is_valid():
            self.perform_update(serializer)
            return self.get_success_response(serializer.data, "Expense updated successfully")
        return self.get_error_response("Error updating expense", serializer.errors)

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return self.get_success_response(None, "Expense deleted successfully")

class ExpenseSummaryView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, year, month):
        try:
            # Validate month
            if not (1 <= int(month) <= 12):
                return Response({
                    "status": "error",
                    "message": "Month must be between 1 and 12"
                }, status=status.HTTP_400_BAD_REQUEST)

            total_expenses = Expense.objects.filter(date__year=year,date__month=month).aggregate(total=Sum('price'))['total'] or 0

            # Get recent expenses (last 5) for the month
            recent_expenses = Expense.objects.filter(date__year=year,date__month=month).order_by('-date', '-created_on')[:5]

            response_data = {
                'total_expenses': float(total_expenses),
                'recent_expenses': ExpenseSerializer(recent_expenses, many=True).data
            }

            return Response({
                'status': 'success',
                'message': 'Expense summary retrieved successfully',
                'data': response_data
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error retrieving expense summary: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class EmployeeAttendanceSummaryView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, date):
        try:
            # Get all employees with attendance on the given date
            employees_with_attendance = Employee.objects.filter(employeeattendance__date=date)

            # Get total present and absent counts
            total_present = employees_with_attendance.filter(employeeattendance__is_present=True).count()
            
            total_absent = employees_with_attendance.filter(employeeattendance__is_present=False).count()

            # Get total employees count
            total_employees = Employee.objects.count()

            # Get present employees
            present_employees = employees_with_attendance.filter(employeeattendance__is_present=True).select_related('department').order_by('name')

            # Get absent employees
            absent_employees = employees_with_attendance.filter(employeeattendance__is_present=False).select_related('department').order_by('name')

            # Prepare response data
            response_data = {
                'date': date,
                'attendance_summary': {
                    'total_employees': total_employees,
                    'total_present': total_present,
                    'total_absent': total_absent,
                    'attendance_percentage': round((total_present / total_employees * 100), 2) if total_employees > 0 else 0
                },
                'present_employees': [{
                    'id': emp.id,
                    'name': emp.name,
                    'department': emp.department.name if emp.department else None,
                    'phone': emp.phone,
                    'email': emp.email
                } for emp in present_employees],
                'absent_employees': [{
                    'id': emp.id,
                    'name': emp.name,
                    'department': emp.department.name if emp.department else None,
                    'phone': emp.phone,
                    'email': emp.email
                } for emp in absent_employees]
            }

            return Response({
                'status': 'success',
                'message': f'Attendance summary for {date}: {total_present} present, {total_absent} absent out of {total_employees} total employees',
                'data': response_data
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error retrieving attendance summary: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class BulkEmployeeMessageView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            message = request.data.get('message')
            if not message:
                return self.get_error_response('Message is required')

            # Get all active employees with phone numbers
            employees = Employee.objects.filter(
                is_active=True,
                phone__isnull=False
            ).exclude(phone='')

            if not employees.exists():
                return self.get_error_response('No employees found with valid phone numbers', status_code=status.HTTP_404_NOT_FOUND)

            # Extract phone numbers
            phone_numbers = [employee.phone for employee in employees]

            # Initialize MSG91
            msg91 = SpoorthiBulkSMSClient()

            # Send bulk SMS
            result = msg91.send_bulk_sms(phone_numbers, message)

            if result['status'] == 'success':
                return self.get_success_response({
                    'total_recipients': len(phone_numbers),
                    'message': message,
                    'msg91_response': result['data']
                }, f'Message sent successfully to {len(phone_numbers)} employees')
            else:
                return self.get_error_response(result['message'])

        except Exception as e:
            return self.get_error_response(f'Error sending messages: {str(e)}')

from dateutil.relativedelta import relativedelta

class ExpenseSalarySummaryView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            year = request.query_params.get('year')
            month = request.query_params.get('month')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            expense_queryset = Expense.objects.all()
            salary_queryset = SalaryRecord.objects.filter(is_paid=True)
            fees_queryset = Fees.objects.all()
            students_queryset = Student.objects.all()

            # Apply date range filtering if provided
            if start_date and end_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

                expense_queryset = expense_queryset.filter(date__range=[start_date, end_date])
                salary_queryset = salary_queryset.filter(payment_date__range=[start_date, end_date])
                fees_queryset = fees_queryset.filter(payment_date__range=[start_date, end_date])
                # For initial fees, we need to check if admission date is in range
                students_queryset = students_queryset.filter(date_of_admission__range=[start_date, end_date])

            else:
                if year:
                    year = int(year)
                    expense_queryset = expense_queryset.filter(date__year=year)
                    salary_queryset = salary_queryset.filter(payment_date__year=year)
                    fees_queryset = fees_queryset.filter(payment_date__year=year)
                    students_queryset = students_queryset.filter(date_of_admission__year=year)

                if month and year:
                    month = int(month)
                    expense_queryset = expense_queryset.filter(date__month=month, date__year=year)
                    salary_queryset = salary_queryset.filter(payment_date__month=month, payment_date__year=year)
                    fees_queryset = fees_queryset.filter(payment_date__month=month, payment_date__year=year)
                    students_queryset = students_queryset.filter(date_of_admission__month=month, date_of_admission__year=year)

            # Total calculations
            # Calculate total expenses by multiplying price with quantity for each item
            total_expenses = sum(
                (expense.price * expense.quantity) if expense.price and expense.quantity else 0 
                for expense in expense_queryset
            )
            
            total_salaries = salary_queryset.aggregate(total=Sum('total_salary'))['total'] or 0

            # Calculate total revenue from both initial fees and regular fee payments
            total_initial_fees = students_queryset.aggregate(
                total=Sum('initial_fee_paid')
            )['total'] or 0
            
            total_fee_payments = fees_queryset.aggregate(
                total=Sum('amount')
            )['total'] or 0
            
            total_revenue = float(total_initial_fees) + float(total_fee_payments)

            # Last 3 months summary using payment_date
            today = date.today().replace(day=1)
            last_three_months = [(today - relativedelta(months=i)) for i in range(3)]
            monthly_summary = {}

            for dt in last_three_months:
                m, y = dt.month, dt.year
                label = f"{month_name[m]} {y}"

                # Calculate expenses with quantity considered
                exp_total = sum(
                    (exp.price * exp.quantity) if exp.price and exp.quantity else 0
                    for exp in Expense.objects.filter(date__month=m, date__year=y)
                )

                sal_total = SalaryRecord.objects.filter(
                    payment_date__month=m,
                    payment_date__year=y,
                    is_paid=True
                ).aggregate(total=Sum('total_salary'))['total'] or 0

                # Calculate revenue for the month (initial fees + regular payments)
                month_initial_fees = Student.objects.filter(
                    date_of_admission__month=m,
                    date_of_admission__year=y
                ).aggregate(total=Sum('initial_fee_paid'))['total'] or 0

                month_fee_payments = Fees.objects.filter(
                    payment_date__month=m,
                    payment_date__year=y
                ).aggregate(total=Sum('amount'))['total'] or 0

                monthly_summary[label] = {
                    "expenses": float(exp_total),
                    "salaries": float(sal_total),
                    "revenue": float(month_initial_fees) + float(month_fee_payments)
                }

            return Response({
                "status": "success",
                "data": {
                    "total_expenses": float(total_expenses),
                    "total_salaries": float(total_salaries),
                    "total_revenue": float(total_revenue),
                    "monthly_summary": monthly_summary,
                    "filters": {
                        "year": year if year else None,
                        "month": month if month else None,
                        "start_date": start_date.strftime('%Y-%m-%d') if start_date else None,
                        "end_date": end_date.strftime('%Y-%m-%d') if end_date else None
                    }
                }
            })

        except Exception as e:
            return Response({
                "status": "error",
                "message": f'Error retrieving totals: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)   
            
class EmployeeAttendanceStatusSummaryView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        today = now().date()
        attendances = EmployeeAttendance.objects.filter(date=today)
        
        total_employees = Employee.objects.filter(is_active=True).count()
        
        present_count = attendances.filter(is_present=True).count()
        absent_count = attendances.filter(is_present=False).count()
        late_count = attendances.filter(
            is_present=True,
            check_in_time__gt=time(10, 0)  # assuming 10:00 AM as late
        ).count()
        under_time_count = attendances.filter(
            is_present=True,
            check_out_time__lt=time(17, 0)  # assuming before 5:00 PM as under time
        ).count()
        sick_leave_count = attendances.filter(
            remarks__icontains='sick'
        ).count()

        return Response({
            'status': 'success',
            'data': {
                'date': str(today),
                'total_employees': total_employees,
                'present': present_count,
                'late': late_count,
                'under_time': under_time_count,
                'absent': absent_count,
                'sick_leave': sick_leave_count,
            }
        })        
        
class WorkingDaysCountView(generics.ListAPIView):
    permission_classes = [IsAuthenticated]

    def get_success_response(self, data, message="Success"):
        return Response({
            "status": "success",
            "message": message,
            "data": data
        })

    def get_error_response(self, message, status_code=status.HTTP_400_BAD_REQUEST):
        return Response({
            "status": "error",
            "message": message,
        }, status=status_code)

    def get(self, request, *args, **kwargs):
        year = request.query_params.get('year')
        month = request.query_params.get('month')

        try:
            year = int(year)
            month = int(month)
        except (TypeError, ValueError):
            return self.get_error_response("Please provide valid 'year' and 'month' as integers.")

        if month < 1 or month > 12:
            return self.get_error_response("Month must be between 1 and 12.")

        _, num_days = calendar.monthrange(year, month)
        start_date = date(year, month, 1)
        end_date = date(year, month, num_days)

        all_dates = [start_date + timedelta(days=i) for i in range(num_days)]

        weekdays = [d for d in all_dates if d.weekday() < 6]

        holidays_qs = Holiday.objects.filter(date__range=(start_date, end_date))
        holidays = set(holidays_qs.values_list('date', flat=True))
        working_days = [d for d in weekdays if d not in holidays]

        return self.get_success_response({
            "year": year,
            "month": month,
            "holidays": len(holidays),
            "working_days": len(working_days)
        })






# class EmpAllAttendanceView(APIView):
#     permission_classes = [IsAuthenticated]

#     def get(self, request):
#         try:
#             # Get query parameters
#             date = request.query_params.get('date')
#             department = request.query_params.get('department')
#             name = request.query_params.get('name')

#             # Base queryset for all active employees
#             employees = Employee.objects.filter(is_active=True, is_deleted=False)

#             # Apply filters if provided
#             if department:
#                 employees = employees.filter(department__name__icontains=department)
#             if name:
#                 employees = employees.filter(name__icontains=name)

#             # If date is provided, get attendance for that date
#             if date:
#                 try:
#                     date = datetime.strptime(date, '%Y-%m-%d').date()
#                 except ValueError:
#                     return Response({
#                         "status": "error",
#                         "message": "Invalid date format. Use YYYY-MM-DD"
#                     }, status=status.HTTP_400_BAD_REQUEST)

#                 # Get employees with attendance records for the date
#                 employees_with_attendance = employees.filter(
#                     employeeattendance__date=date
#                 ).select_related('department').distinct()

#                 # Get present and absent employees
#                 present_employees = employees_with_attendance.filter(
#                     employeeattendance__date=date,
#                     employeeattendance__is_present=True
#                 ).distinct()

#                 absent_employees = employees_with_attendance.filter(
#                     employeeattendance__date=date,
#                     employeeattendance__is_present=False
#                 ).distinct()

#                 # Prepare response data
#                 response_data = {
#                     "date": date,
#                     "summary": {
#                         "total_employees": employees.count(),
#                         "total_with_attendance": employees_with_attendance.count(),
#                         "present_count": present_employees.count(),
#                         "absent_count": absent_employees.count()
#                     },
#                     "present_employees": [{
#                         "id": employee.id,
#                         "name": employee.name,
#                         "employee_id": employee.employee_id,
#                         "department": employee.department.name if employee.department else None,
#                         "designation": employee.designation,
#                         "check_in_time": employee.employeeattendance_set.filter(date=date).first().check_in_time if employee.employeeattendance_set.filter(date=date).exists() else None,
#                         "check_out_time": employee.employeeattendance_set.filter(date=date).first().check_out_time if employee.employeeattendance_set.filter(date=date).exists() else None
#                     } for employee in present_employees],
#                     "absent_employees": [{
#                         "id": employee.id,
#                         "name": employee.name,
#                         "employee_id": employee.employee_id,
#                         "department": employee.department.name if employee.department else None,
#                         "designation": employee.designation,
#                         "remarks": employee.employeeattendance_set.filter(date=date).first().remarks if employee.employeeattendance_set.filter(date=date).exists() else None
#                     } for employee in absent_employees]
#                 }

#             else:
#                 # If no date provided, return all employees with their latest attendance
#                 response_data = {
#                     "summary": {
#                         "total_employees": employees.count()
#                     },
#                     "employees": []
#                 }

#                 for employee in employees:
#                     latest_attendance = EmployeeAttendance.objects.filter(
#                         employee=employee
#                     ).order_by('-date').first()

#                     employee_data = {
#                         "id": employee.id,
#                         "name": employee.name,
#                         "employee_id": employee.employee_id,
#                         "department": employee.department.name if employee.department else None,
#                         "designation": employee.designation,
#                         "latest_attendance": {
#                             "date": latest_attendance.date if latest_attendance else None,
#                             "is_present": latest_attendance.is_present if latest_attendance else None,
#                             "check_in_time": latest_attendance.check_in_time if latest_attendance else None,
#                             "check_out_time": latest_attendance.check_out_time if latest_attendance else None,
#                             "remarks": latest_attendance.remarks if latest_attendance else None
#                         }
#                     }
#                     response_data["employees"].append(employee_data)

#             return Response({
#                 "status": "success",
#                 "message": "Employee attendance summary retrieved successfully",
#                 "data": response_data
#             })

#         except Exception as e:
#             return Response({
#                 "status": "error",
#                 "message": f"Error retrieving employee attendance summary: {str(e)}"
#             }, status=status.HTTP_400_BAD_REQUEST)
