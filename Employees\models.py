from django.db import models
from django.core.validators import EmailValidator, RegexValidator, MinValueValidator
from django.utils import timezone
from Users.models import CoreModel, getcode
from Users.storage_backends import MediaStorage

# Create your models here.

class Department(CoreModel):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    # code = models.CharField(max_length=10, null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
    
    CODE_PREFIX = 'DEPT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Department,'DEPT')
        super(Department, self).save(*args, **kwargs)

class Category(CoreModel):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    # code = models.Char<PERSON>ield(max_length=10, null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']


    CODE_PREFIX = 'CAT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Category,'CAT')
        super(Category, self).save(*args, **kwargs)
        
        
class Employee(CoreModel):
    # Basic Information
    employee_no = models.CharField(max_length=20, unique=True, blank=True)
    name = models.CharField(max_length=100,blank=True)
    photo = models.ImageField(upload_to='employee_photos/', null=True, blank=True,storage=MediaStorage())
    email = models.EmailField(unique=True,blank=True)
    phone = models.CharField(max_length=15,blank=True)
    address = models.TextField(null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    joining_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    # code = models.CharField(max_length=10, null=True, blank=True)
    salary = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)], null=True, blank=True)
    # date_of_joining = models.DateField(default=timezone.now, null=True, blank=True)
    
    def __str__(self):
        return f"{self.employee_no} - {self.name}"


    # def save(self, *args, **kwargs):
    #     if not self.employee_no:
    #         self.generate_employee_no()
    #     super().save(*args, **kwargs)

    class Meta:
        ordering = ['name']
        
    CODE_PREFIX = 'EMP'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Employee,'EMP')
        super(Employee, self).save(*args, **kwargs)

class EmployeeAttendance(CoreModel):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendance_records')
    date = models.DateField()
    is_present = models.BooleanField(default=True)
    check_in_time = models.TimeField(null=True, blank=True)
    check_out_time = models.TimeField(null=True, blank=True)
    remarks = models.TextField(blank=True, null=True)
    # code = models.CharField(max_length=10, null=True, blank=True)

    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date', 'employee__name']

    def __str__(self):
        return f"{self.employee.name} - {self.date} - {'Present' if self.is_present else 'Absent'}"

    CODE_PREFIX = 'EATT'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(EmployeeAttendance,'EATT')
        super(EmployeeAttendance, self).save(*args, **kwargs)
        
class Leave(CoreModel):
    LEAVE_TYPES = [
        ('CL', 'Casual Leave'),
        ('SL', 'Sick Leave'),
        ('PL', 'Privilege Leave'),
        ('LWP', 'Leave Without Pay'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leaves')
    leave_type = models.CharField(max_length=3, choices=LEAVE_TYPES)
    start_date = models.DateField()
    end_date = models.DateField()
    reason = models.TextField()
    is_approved = models.BooleanField(default=False)
    # code = models.CharField(max_length=10, null=True, blank=True)

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.name} - {self.get_leave_type_display()} ({self.start_date} to {self.end_date})"


    CODE_PREFIX = 'LEAVE'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Leave,'LEAVE')
        super(Leave, self).save(*args, **kwargs)
        
class Holiday(CoreModel):
    name = models.CharField(max_length=100)
    date = models.DateField(unique=True)
    description = models.TextField(blank=True, null=True)
    # code = models.CharField(max_length=10, null=True, blank=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"{self.name} - {self.date}"

    CODE_PREFIX = 'HOL'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Holiday,'HOL')
        super(Holiday, self).save(*args, **kwargs)
        
class SalaryRecord(CoreModel):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='salary_records',null=True)
    month = models.IntegerField(null=True, blank=True)
    year = models.IntegerField(null=True, blank=True)
    basic_salary = models.DecimalField(max_digits=10, decimal_places=2,null=True, blank=True)
    total_working_days = models.IntegerField(null=True, blank=True)
    present_days = models.IntegerField(null=True, blank=True)
    absent_days = models.IntegerField(null=True, blank=True)
    holiday_days = models.IntegerField(null=True, blank=True)
    per_day_salary = models.DecimalField(max_digits=10, decimal_places=2,null=True, blank=True)
    working_days_salary = models.DecimalField(max_digits=10, decimal_places=2,null=True, blank=True)
    holiday_salary = models.DecimalField(max_digits=10, decimal_places=2,null=True, blank=True)
    total_salary = models.DecimalField(max_digits=10, decimal_places=2,null=True, blank=True)
    net_salary = models.DecimalField(max_digits=10, decimal_places=2,null=True, blank=True)
    # code = models.CharField(max_length=10, null=True, blank=True)
    is_paid = models.BooleanField(default=False)
    payment_date = models.DateField(null=True, blank=True)
    transcaction_id = models.CharField(max_length=100, unique=True, null=True, blank=True)
    
    class Meta:
        unique_together = ('employee', 'month', 'year')
        ordering = ['-year', '-month']

    def __str__(self):
        return f"{self.employee} - {self.month}/{self.year}"


    CODE_PREFIX = 'SAL'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(SalaryRecord,'SAL')
        super(SalaryRecord, self).save(*args, **kwargs)
        
        
class Expense(CoreModel):
    name = models.CharField(max_length=100,blank=True, null=True)
    quantity = models.PositiveIntegerField(default=1,blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)],blank=True, null=True)
    transaction_id = models.CharField(max_length=100, unique=True,blank=True, null=True)
    seller_phone = models.CharField(max_length=15,blank=True, null=True)
    bill_image = models.ImageField(upload_to='expense_bills/', null=True, blank=True)
    date = models.DateField(default=timezone.now,blank=True, null=True)

    def __str__(self):
        return f"{self.name} - {self.transaction_id}"

    class Meta:
        ordering = ['-date']
        verbose_name = 'Expense'
        verbose_name_plural = 'Expenses'


    CODE_PREFIX = 'EXP'

        
    def save(self, *args, **kwargs):
        if self.id is None and (self.code == "" or self.code == None):
            self.code = getcode(Expense,'EXP')
        super(Expense, self).save(*args, **kwargs)