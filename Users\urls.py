from django.urls import path
from .views import ChangePasswordView, RegisterView, LoginView, LogoutView, TokenRefreshView,UserListCreateView, UserRetrieveUpdateDestroyView

urlpatterns = [
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    path('users/', UserListCreateView.as_view(), name='user-list-create'),
    path('users/<uuid:pk>/', UserRetrieveUpdateDestroyView.as_view(), name='user-detail'),

    #change password
    path('change-password/', ChangePasswordView.as_view(), name='change-password'),

]
