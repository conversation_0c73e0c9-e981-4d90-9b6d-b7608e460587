# Generated by Django 5.2.4 on 2025-07-14 15:23

import Users.storage_backends
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Masters', '0033_alter_student_father_aadhar_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='inventoryitem',
            name='image',
            field=models.ImageField(blank=True, null=True, storage=Users.storage_backends.MediaStorage(), upload_to='inventory_image/'),
        ),
        migrations.AlterField(
            model_name='student',
            name='application_form',
            field=models.ImageField(blank=True, max_length=500, null=True, storage=Users.storage_backends.MediaStorage(), upload_to='application_photos/'),
        ),
        migrations.AlterField(
            model_name='student',
            name='photo',
            field=models.ImageField(blank=True, max_length=500, null=True, storage=Users.storage_backends.MediaStorage(), upload_to='student_photos/'),
        ),
        migrations.CreateModel(
            name='StudentTestResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('test_name', models.CharField(max_length=100)),
                ('test_date', models.DateField(default=django.utils.timezone.now)),
                ('total_marks', models.DecimalField(decimal_places=2, max_digits=6)),
                ('rank', models.PositiveIntegerField(blank=True, null=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Masters.student')),
            ],
            options={
                'unique_together': {('student', 'test_name', 'test_date')},
            },
        ),
        migrations.CreateModel(
            name='StudentTestSubjectMark',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=100)),
                ('marks_obtained', models.DecimalField(decimal_places=2, max_digits=5)),
                ('total_marks', models.DecimalField(decimal_places=2, max_digits=5)),
                ('rank', models.PositiveIntegerField(blank=True, null=True)),
                ('test_result', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subject_marks', to='Masters.studenttestresult')),
            ],
            options={
                'unique_together': {('test_result', 'subject')},
            },
        ),
        migrations.DeleteModel(
            name='TestMarks',
        ),
    ]
