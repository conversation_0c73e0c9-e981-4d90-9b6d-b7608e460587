# Generated by Django 5.2.1 on 2025-07-05 12:08

import Users.storage_backends
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Masters', '0030_alter_student_application_form'),
    ]

    operations = [
        migrations.AlterField(
            model_name='inventoryitem',
            name='image',
            field=models.ImageField(blank=True, null=True, storage=Users.storage_backends.MediaStorage(), upload_to='inventory_image/'),
        ),
        migrations.AlterField(
            model_name='student',
            name='batch',
            field=models.CharField(choices=[('MPC', 'MPC'), ('BiPC', 'BIPC'), ('cec', 'CEC'), ('mec', 'MEC')], max_length=20),
        ),
        migrations.AlterField(
            model_name='student',
            name='group',
            field=models.CharField(choices=[('CO-SPARK STAR', 'CO-SPARK STAR'), ('CO-SPARK  GIRLS', 'CO-SPARK  GIRLS'), ('CO-SPARK BOYS', 'CO-SPARK BOYS'), ('S-SPARK STAR', 'S-SPARK STAR'), ('S-SPARK BOYS', 'S-SPARK BOYS'), ('SPARK GIRLS', 'SPARK GIRLS'), ('SPARK BOYS', 'SPARK BOYS'), ('SPARK III', 'SPARK III'), ('SMPL', 'SMPL')], max_length=20),
        ),
    ]
