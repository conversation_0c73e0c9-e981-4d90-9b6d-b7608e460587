from django.urls import path
from .views import DepartmentListCreateView, DepartmentDetailView,CategoryListCreateView, CategoryDetailView, EmployeeAttendanceStatusSummaryView,EmployeeListCreateView, EmployeeDetailView,EmployeeAttendanceListCreateView, EmployeeAttendanceDetailView,EmployeeAttendanceView, BulkEmployeeAttendanceUploadView, ExpenseSalarySummaryView,LeaveListCreateView, LeaveDetailView,HolidayListCreateView, HolidayDetailView,BulkHolidayUploadView, SalaryCalculationView,EmployeeSalaryHistoryView, SalaryRecordListView,SalaryRecordDetailView, ExpenseListCreateView,ExpenseDetailView, ExpenseSummaryView,EmployeeAttendanceSummaryView, BulkEmployeeMessageView, WorkingDaysCountView

urlpatterns = [
    # Department URLs
    path('departments/', DepartmentListCreateView.as_view(), name='department-list-create'),
    path('departments/<uuid:pk>/', DepartmentDetailView.as_view(), name='department-detail'),

    # Category URLs
    path('categories/', CategoryListCreateView.as_view(), name='category-list-create'),
    path('categories/<uuid:pk>/', CategoryDetailView.as_view(), name='category-detail'),

    # Employee URLs
    path('employees/', EmployeeListCreateView.as_view(), name='employee-list-create'),
    path('employees/<uuid:pk>/', EmployeeDetailView.as_view(), name='employee-detail'),
    path('employees/<uuid:employee_id>/salary-history/', EmployeeSalaryHistoryView.as_view(), name='employee-salary-history'),

    # Employee Attendance URLs
    path('attendance/', EmployeeAttendanceListCreateView.as_view(), name='attendance-list-create'),
    path('attendance/<uuid:pk>/', EmployeeAttendanceDetailView.as_view(), name='attendance-detail'),
    path('attendance/employee/<uuid:employee_id>/', EmployeeAttendanceView.as_view(), name='employee-attendance'),
    path('attendance/bulk-upload/', BulkEmployeeAttendanceUploadView.as_view(), name='bulk-attendance-upload'),
    path('attendance/summary/<str:date>/', EmployeeAttendanceSummaryView.as_view(), name='attendance-summary'),

    # Leave URLs
    path('leaves/', LeaveListCreateView.as_view(), name='leave-list-create'),
    path('leaves/<uuid:pk>/', LeaveDetailView.as_view(), name='leave-detail'),

    # Holiday URLs
    path('holidays/', HolidayListCreateView.as_view(), name='holiday-list-create'),
    path('holidays/<uuid:pk>/', HolidayDetailView.as_view(), name='holiday-detail'),
    path('holidays/bulk-upload/', BulkHolidayUploadView.as_view(), name='bulk-holiday-upload'),

    # Salary Calculation URL
    path('salary/calculate/<uuid:employee_id>/<int:year>/<int:month>/', SalaryCalculationView.as_view(), name='salary-calculation'),

    # Salary Record URLs
    path('salary-records/', SalaryRecordListView.as_view(), name='salary-record-list'),
    path('salary-records/<uuid:pk>/', SalaryRecordDetailView.as_view(), name='salary-record-detail'),

    # Expense URLs
    path('expenses/', ExpenseListCreateView.as_view(), name='expense-list-create'),
    path('expenses/<uuid:pk>/', ExpenseDetailView.as_view(), name='expense-detail'),
    # path('expenses/summary/', ExpenseSummaryView.as_view(), name='expense-summary'),
    
    
    
    path('total-expenses/', ExpenseSalarySummaryView.as_view(), name='expense-salary-summary'),

    # Bulk Message URL
    path('employees/bulk-message/', BulkEmployeeMessageView.as_view(), name='bulk-employee-message'),
    
    # Employee Attendance Status Summary URL
    path('attendance-status-summary/', EmployeeAttendanceStatusSummaryView.as_view(), name='attendance-status-summary'),
    
    # Working Days Count URL
    path('working-days-count/', WorkingDaysCountView.as_view(), name='working-days-count'),

    # path('employee-attendance-summary/', EmpAllAttendanceView.as_view(), name='employee-attendance-summary'),

] 