"""
Django settings for spoorthi_crm project.

Generated by 'django-admin startproject' using Django 5.1.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""
import dj_database_url
import os
from pathlib import Path
from datetime import timedelta
from dotenv import load_dotenv
from mongoengine import connect

from Users.storage_backends import MediaStorage
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-2yyx=rt%-u1q5s74+osdh#@_-jbpt#c6n+2pz)^4%e0kq*bv6*'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = ['*','spoorthi-crm.onrender.com']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_filters',
    'corsheaders',
    'storages',

    'rest_framework',
    'rest_framework_simplejwt',
    'import_export',
    'drf_yasg',
    'Users',
    'Masters',
    'Employees',
]

AUTH_USER_MODEL = 'Users.User'

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    # 'django.middleware.cache.UpdateCacheMiddleware',
    'django.middleware.common.CommonMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 'django.middleware.cache.FetchFromCacheMiddleware',
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    # 'DEFAULT_PAGINATION_CLASS': 'utils.pagination.CustomPagination',
    # 'PAGE_SIZE': 25,
}

SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    },
    # 'USE_SESSION_AUTH':False,
    'LOGIN_URL':'/admin/login/',
    'LOGOUT_URL':'/admin/logout/'
    
}

# JWT Settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=7),  # Access token expires in 7 days
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),  # Refresh token expires in 30 days
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
}

AUTHENTICATION_BACKENDS = [
    'Users.backends.EmailBackend',
]


ROOT_URLCONF = 'spoorthi_crm.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'build'),os.path.join('templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'spoorthi_crm.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# MONGODB_NAME = os.getenv('MONGODB_NAME', 'spoorthi')
# MONGODB_URI = os.getenv(
    # 'MONGODB_URI',
    # 'mongodb+srv://username:<EMAIL>/spoorthi?retryWrites=true&w=majority'
# )

# connect(
    # db=MONGODB_NAME,
    # host=MONGODB_URI,
    # alias='default'
# )
# from pymongo import MongoClient
# client = MongoClient(os.getenv("MONGODB_URI"))
# db = client['db_name']  # Replace 'db_name' with your actual database name
# DATABASES["default"] =dj_database_url.parse("***********************************************************************************************************") #live database
# DATABASES["default"] =dj_database_url.parse("*********************************************************************************************************")  #test database
#*****************************************************************************************************
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'spoorthi_crm',
#         'USER': 'root',
#         'PASSWORD': 'Qwerty@1029',
#         'HOST': '127.0.0.1',
#         'PORT': '3306',
#     }
# }

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Kolkata'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# --- AWS S3 Media Storage Configuration ---
# Required environment variables:
#   AWS_ACCESS_KEY_ID
#   AWS_SECRET_ACCESS_KEY
#   AWS_STORAGE_BUCKET_NAME
#   AWS_S3_REGION_NAME
#   USE_S3 (set to 'True' to enable S3, 'False' for local)

USE_S3 = True
print(f"USE_S3 is set to: {USE_S3}")
if USE_S3 == True:
    print("✅ Using S3 for media storage")
    AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
    print(f"AWS_ACCESS_KEY_ID: {AWS_ACCESS_KEY_ID}")
    AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    print(f"AWS_SECRET_ACCESS_KEY: {AWS_SECRET_ACCESS_KEY}")
    AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME")
    print(f"AWS_STORAGE_BUCKET_NAME: {AWS_STORAGE_BUCKET_NAME}")
    AWS_S3_REGION_NAME = os.getenv("AWS_S3_REGION_NAME")
    print(f"AWS_S3_REGION_NAME: {AWS_S3_REGION_NAME}")
    AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com'
    print(f"AWS_S3_CUSTOM_DOMAIN: {AWS_S3_CUSTOM_DOMAIN}")
    DEFAULT_FILE_STORAGE = 'Users.storage_backends.MediaStorage'
    MEDIA_ROOT = ''  # Not used with S3
    MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/media/'

    AWS_QUERYSTRING_AUTH = False
    AWS_S3_FILE_OVERWRITE = False
    AWS_DEFAULT_ACL = 'public-read'
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }
else:
    print("🛠️ Using local storage for media files")
    MEDIA_URL = '/media/'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
    DEFAULT_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'

    # Storage backend - must point to your custom class
    # DEFAULT_FILE_STORAGE = 'Users.storage_backends.MediaStorage'
    # Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

    # Optional: Use S3 for static files in production
    # STATICFILES_STORAGE = 'storages.backends.s3boto3.S3StaticStorage'
    # STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/static/'
# MEDIA_URL = '/media/'
# MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# MSG91 Settings
MSG91_AUTH_KEY = 'your_auth_key_here'  # Replace with your MSG91 auth key
MSG91_SENDER_ID = 'your_sender_id'

# Remove 2Factor.in Settings
# TWO_FACTOR_API_KEY = 'ccdd7d9d-42b5-11ef-8b60-0200cd936042'  # Your 2factor.in API key
# TWO_FACTOR_SENDER_ID = 'SPOORTHI'

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True

# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:64785",
# ]


IO_SERVER_URL = "http://localhost:5000"

# Redis Configuration
# REDIS_HOST = 'localhost'  # Redis server host
# REDIS_PORT = 6379        # Redis server port

# REDIS_URL = os.getenv("REDIS_URL")

# Cache settings
# CACHES = {
#     "default": {
#         "BACKEND": "django_redis.cache.RedisCache",
#         "LOCATION": REDIS_URL,
#         "OPTIONS": {
#             "CLIENT_CLASS": "django_redis.client.DefaultClient",
#             "SOCKET_CONNECT_TIMEOUT": 5,
#             "SOCKET_TIMEOUT": 5,
#             "RETRY_ON_TIMEOUT": True,
#             "MAX_CONNECTIONS": 1000,
#             "CONNECTION_POOL_KWARGS": {"max_connections": 100}
#         },
#         "KEY_PREFIX": "spoorthi_crm"
#     }
# }
# print("Redis URL:", REDIS_URL)
# Cache timeout in seconds (5 minutes)
# CACHE_TTL = 300

# Use Redis for session storage
# SESSION_ENGINE = "django.contrib.sessions.backends.cache"
# SESSION_CACHE_ALIAS = "default"

# Cache middleware settings
# MIDDLEWARE = [
#     'django.middleware.security.SecurityMiddleware',
#     'whitenoise.middleware.WhiteNoiseMiddleware',
#     'django.contrib.sessions.middleware.SessionMiddleware',
#     'django.middleware.cache.UpdateCacheMiddleware',
#     'django.middleware.common.CommonMiddleware',
#     'corsheaders.middleware.CorsMiddleware',
#     'django.middleware.common.CommonMiddleware',
#     'django.middleware.csrf.CsrfViewMiddleware',
#     'django.contrib.auth.middleware.AuthenticationMiddleware',
#     'django.contrib.messages.middleware.MessageMiddleware',
#     'django.middleware.clickjacking.XFrameOptionsMiddleware',
#     'django.middleware.cache.FetchFromCacheMiddleware',
# ]

# Cache settings for views
# CACHE_MIDDLEWARE_ALIAS = 'default'
# CACHE_MIDDLEWARE_SECONDS = 300  # 5 minutes
# CACHE_MIDDLEWARE_KEY_PREFIX = 'spoorthi_crm'

# AWS S3 storage configuration for static files
# AWS_ACCESS_KEY_ID = '********************'
# AWS_SECRET_ACCESS_KEY = 'EmhmoKVQKpiFuEjNUMBs5/6/QpgTzKpaPRNuhQJ2'
# AWS_STORAGE_BUCKET_NAME = 'spoorthi-crm'
# AWS_S3_FILE_OVERWRITE = False
# AWS_DEFAULT_ACL = None

# Use S3Boto3Storage for static files
# STATICFILES_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'



# AWS Credentials
# AWS_ACCESS_KEY_ID = '********************'
# AWS_SECRET_ACCESS_KEY = 'swyFqCGb9gRxgFQ7PP2zaAiu7DyxsfY5DYpXpPy+'
# AWS_STORAGE_BU/CKET_NAME = 'spoorthicrm'
# AWS_S3_REGION_NAME = 'ap-south-1'
# AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com'

# Media files (uploads)
# DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

# Static files (optional)
# STATICFILES_STORAGE = 'storages.backends.s3boto3.S3StaticStorage'

# MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'

# Optional: If using static files on S3
# STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/static/'
# DEFAULT_FILE_STORAGE = 'cloudinary_storage.storage.MediaCloudinaryStorage'

# AWS S3 Configuration
# AWS S3 Settings
# AWS S3 Configuration (for both local and production)
# AWS_ACCESS_KEY_ID = '********************'  # Consider using env vars for security
# AWS_SECRET_ACCESS_KEY = 'EmhmoKVQKpiFuEjNUMBs5/6/QpgTzKpaPRNuhQJ2'  # Use env vars!
# AWS_STORAGE_BUCKET_NAME = 'spoorthi-crm'
# AWS_S3_REGION_NAME = 'ap-south-1'
# AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com'
# AWS_DEFAULT_ACL = 'public-read'  # Ensures files are readable
# AWS_QUERYSTRING_AUTH = False  # Avoids signed URLs for simplicity

# File storage settings
# DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
# MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'

# Remove these local storage settings if present
# MEDIA_ROOT = None